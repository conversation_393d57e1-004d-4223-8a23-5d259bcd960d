# Payment Middleware Service - Enhanced Reporting Features

## Overview

This document describes the comprehensive reporting features implemented for the payment middleware service. The implementation includes three major reporting capabilities:

1. **Updated Quarterly Payment Report** - Modified to return business data directly without ApiResponse wrapper
2. **Residence Bill Quarterly Report** - New feature for quarterly residence billing reports
3. **Custom SQL Query Executor** - Secure dynamic query execution with comprehensive validation

All features use a read-only database replica to avoid impacting main database operations and return business data directly for simplified integration.

## Features

- **Read-Only Database Connection**: Uses a separate read-only database replica to avoid impacting main database operations
- **Quarterly Aggregation**: Groups payment data by quarter and product type
- **REST API**: Provides a clean REST endpoint for retrieving report data
- **Comprehensive Testing**: Includes unit tests, integration tests, and error handling tests
- **Proper Error Handling**: Returns structured error responses for various failure scenarios
- **SQL Injection Protection**: Uses parameterized queries and proper input validation

## API Endpoints

### 1. GET /api/reports/quarterly-payments

**Updated Quarterly Payment Report** - Returns business data directly without wrapper.

#### Response Format (Direct Business Data)

```json
[
  {
    "quarter": "2024Q1",
    "product_type": "premium",
    "order_count": 150,
    "total_amount": 45000.00
  },
  {
    "quarter": "2024Q1",
    "product_type": "standard",
    "order_count": 200,
    "total_amount": 30000.00
  }
]
```

### 2. GET /api/reports/residence-bills

**Residence Bill Quarterly Report** - Provides quarterly residence billing reports.

#### Response Format (Direct Business Data)

```json
[
  {
    "quarter": "2024Q1",
    "product_type": "residential",
    "category": "maintenance_fee",
    "count": 150,
    "sum_amount": 45000.00
  },
  {
    "quarter": "2024Q1",
    "product_type": "commercial",
    "category": "parking_fee",
    "count": 75,
    "sum_amount": 22500.00
  }
]
```

### 3. POST /api/reports/custom-query

**Custom SQL Query Executor** - Executes secure read-only SQL queries.

#### Request Format

```json
{
  "query": "SELECT product_type, COUNT(*) as count FROM db_prod_kip_payment_middleware.pmw_order_info GROUP BY product_type"
}
```

#### Response Format (Direct Business Data)

```json
{
  "columns": ["product_type", "count"],
  "rows": [
    ["residential", 150],
    ["commercial", 75]
  ],
  "execution_time_ms": 245,
  "row_count": 2
}
```

#### Error Responses

All endpoints return appropriate HTTP status codes:
- **400 Bad Request**: For validation errors (custom query only)
- **500 Internal Server Error**: For execution errors

## Architecture

### Components

#### 1. Updated Quarterly Payment Report
- **Controller**: `QuarterlyPaymentReportController` (modified to return direct data)
- **Service**: `QuarterlyPaymentReportService` (updated response format)
- **Repository**: `QuarterlyPaymentReportRepository` (existing)
- **DTOs**: `QuarterlyPaymentReportDto`, `QuarterlyPaymentReportResponse`

#### 2. Residence Bill Quarterly Report
- **Controller**: `ResidenceBillReportController`
- **Service**: `ResidenceBillReportService` and `ResidenceBillReportServiceImpl`
- **Repository**: `ResidenceBillReportRepository` and `ResidenceBillReportRepositoryImpl`
- **DTOs**: `ResidenceBillReportDto`, `ResidenceBillReportResponse`

#### 3. Custom SQL Query Executor
- **Controller**: `CustomQueryController`
- **Service**: `CustomQueryService` and `CustomQueryServiceImpl`
- **Repository**: `CustomQueryRepository` and `CustomQueryRepositoryImpl`
- **Validator**: `SqlQueryValidator` (comprehensive security validation)
- **DTOs**: `CustomQueryRequest`, `CustomQueryResponse`

### Database Configuration

All features use a separate read-only database configuration:

- **Primary Database**: Used for normal application operations
- **Read-Only Replica**: Used exclusively for reporting queries to avoid performance impact

### SQL Queries

#### Quarterly Payment Report Query
```sql
SELECT
    quarter_name as quarter,
    product_type,
    COUNT(DISTINCT pay_unit_id) as order_count,
    SUM(amount) as total_amount
FROM (
    SELECT
        CONCAT(YEAR(A.created_date), 'Q', QUARTER(A.created_date)) as quarter_name,
        D.product_type,
        A.pay_unit_id,
        B.amount
    FROM
        db_prod_kip_payment_middleware.pmw_confirm_info A
    INNER JOIN
        db_prod_kip_payment_middleware.pmw_pay_unit_info B
        ON A.pay_unit_id = B.pay_unit_id
    INNER JOIN
        db_prod_kip_payment_middleware.pmw_order_info D
        ON B.order_no = D.order_no
    WHERE
        A.created_date >= DATE_FORMAT(DATE_SUB(CONCAT(YEAR(NOW()), '-', (QUARTER(NOW()) - 1) * 3 + 1, '-01'), INTERVAL 3 MONTH), '%Y-%m-%d')
) AS derived_table
GROUP BY
    quarter_name, product_type
ORDER BY
    quarter_name, product_type;
```

#### Residence Bill Report Query
```sql
SELECT
    CONCAT(YEAR(a.created_date), 'Q', QUARTER(a.created_date)) as quarter,
    d.product_type,
    d.category,
    COUNT(*) as count,
    SUM(b.amount) as sum_amount
FROM db_prod_kip_payment_middleware.pmw_confirm_info a
INNER JOIN db_prod_kip_payment_middleware.pmw_pay_unit_info b ON a.pay_unit_id = b.pay_unit_id
LEFT JOIN db_prod_kip_payment_middleware.pmw_order_info d ON b.order_no = d.order_no
WHERE a.created_date >= DATE_FORMAT(CONCAT(YEAR(NOW()), '-01-01'), '%Y-%m-%d 00:00:00')
  AND a.created_date < DATE_FORMAT(CONCAT(YEAR(NOW()) + 1, '-01-01'), '%Y-%m-%d 00:00:00')
GROUP BY quarter, d.category, d.product_type
ORDER BY quarter, d.product_type, d.category
```

### Security Features

#### SQL Query Validation (Custom Query Executor)
The `SqlQueryValidator` provides comprehensive security validation:

- **SELECT-only enforcement**: Only SELECT statements are allowed
- **Forbidden keyword detection**: Blocks INSERT, UPDATE, DELETE, DROP, etc.
- **Dangerous pattern detection**: Prevents SQL injection attempts
- **Table access validation**: Ensures queries only access allowed tables
- **Multiple statement prevention**: Blocks compound SQL statements
- **Query size limits**: Maximum 10,000 characters per query

## Configuration

### Application Properties

Add the following configuration to your application properties:

```yaml
spring:
  # Read-only replica datasource configuration
  datasource-readonly:
    url: ****************************************************************************
    username: your_readonly_username
    password: your_readonly_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      idle-timeout: 600000
      maximum-pool-size: 50
      minimum-idle: 5
      connection-timeout: 30000
      connection-test-query: 'SELECT 1 + 1 FROM DUAL;'
```

## Testing

All features include comprehensive tests:

### Test Coverage

#### 1. Quarterly Payment Report Tests
- **Unit Tests**: `QuarterlyPaymentReportServiceImplTest`
- **Controller Tests**: `QuarterlyPaymentReportControllerTest`
- **Integration Tests**: End-to-end testing with database

#### 2. Residence Bill Report Tests
- **Unit Tests**: `ResidenceBillReportServiceImplTest`
- **Controller Tests**: `ResidenceBillReportControllerTest`
- **Repository Tests**: Database query validation

#### 3. Custom Query Executor Tests
- **Unit Tests**: `CustomQueryServiceImplTest`
- **Controller Tests**: `CustomQueryControllerTest`
- **Validator Tests**: `SqlQueryValidatorTest` (comprehensive security testing)

### Running Tests

```bash
# Run all new reporting feature tests
mvn test -Dtest="*ResidenceBillReport*,*CustomQuery*,SqlQueryValidator*"

# Run specific test classes
mvn test -Dtest="ResidenceBillReportServiceImplTest,CustomQueryServiceImplTest,SqlQueryValidatorTest"

# Run all quarterly payment report tests (existing)
mvn test -Dtest="*QuarterlyPaymentReport*"
```

### Test Features

- **Unit Tests**: Test individual components in isolation with mocked dependencies
- **Integration Tests**: Test complete flow from controller to repository
- **Error Handling Tests**: Verify proper error responses for various failure scenarios
- **Security Tests**: Validate SQL injection prevention and access controls
- **Validation Tests**: Test input validation and boundary conditions

## Usage Examples

### Using curl

```bash
# Get quarterly payment report
curl -X GET "http://localhost:8080/api/reports/quarterly-payments" \
     -H "Content-Type: application/json"

# Get residence bill quarterly report
curl -X GET "http://localhost:8080/api/reports/residence-bills" \
     -H "Content-Type: application/json"

# Execute custom SQL query
curl -X POST "http://localhost:8080/api/reports/custom-query" \
     -H "Content-Type: application/json" \
     -d '{
       "query": "SELECT product_type, COUNT(*) as count FROM db_prod_kip_payment_middleware.pmw_order_info GROUP BY product_type"
     }'
```

### Using Java/Spring RestTemplate

```java
RestTemplate restTemplate = new RestTemplate();

// Quarterly payment report
String quarterlyUrl = "http://localhost:8080/api/reports/quarterly-payments";
List<QuarterlyPaymentReportResponse> quarterlyResponse =
    restTemplate.getForObject(quarterlyUrl, List.class);

// Residence bill report
String residenceUrl = "http://localhost:8080/api/reports/residence-bills";
List<ResidenceBillReportResponse> residenceResponse =
    restTemplate.getForObject(residenceUrl, List.class);

// Custom query
String customUrl = "http://localhost:8080/api/reports/custom-query";
CustomQueryRequest request = new CustomQueryRequest(
    "SELECT product_type, COUNT(*) FROM pmw_order_info GROUP BY product_type"
);
CustomQueryResponse customResponse =
    restTemplate.postForObject(customUrl, request, CustomQueryResponse.class);
```

## Monitoring and Logging

The feature includes comprehensive logging for:

- Request processing
- Query execution
- Error scenarios
- Performance metrics

Log messages are structured and include correlation IDs for tracing.

## Security Considerations

### Database Security
- Uses read-only database connection to prevent data modification
- Separate database replica isolates reporting from operational data
- Proper connection pooling with security configurations

### Query Security (Custom Query Executor)
- **SQL Injection Prevention**: Comprehensive query validation and sanitization
- **Access Control**: Only allows access to specific tables with `pmw_` prefix
- **Operation Restrictions**: Only SELECT statements permitted
- **Pattern Detection**: Blocks dangerous SQL patterns and keywords
- **Size Limits**: Maximum query length enforcement

### General Security
- Implements proper input validation for all endpoints
- Uses parameterized queries where applicable
- Follows existing authentication and authorization patterns
- Comprehensive error handling without information leakage

## Performance Considerations

- Uses read-only replica to avoid impacting main database performance
- Optimized connection pool settings for reporting workloads
- Efficient SQL query with proper indexing considerations
- Implements proper error handling to avoid resource leaks

## Future Enhancements

Potential future improvements:

### Performance Enhancements
1. **Caching**: Add Redis caching for frequently requested reports
2. **Pagination**: Add pagination support for large result sets
3. **Query Optimization**: Add query result caching and optimization
4. **Async Processing**: Add asynchronous report generation for large datasets

### Feature Enhancements
5. **Filtering**: Add date range and product type filtering parameters
6. **Export Formats**: Support CSV, Excel export formats
7. **Scheduled Reports**: Add scheduled report generation and email delivery
8. **Real-time Updates**: Add WebSocket support for real-time report updates

### Custom Query Enhancements
9. **Query Templates**: Pre-defined query templates for common use cases
10. **Query History**: Store and retrieve previously executed queries
11. **Query Sharing**: Allow users to share and collaborate on queries
12. **Advanced Validation**: More sophisticated SQL parsing and validation

### Monitoring Enhancements
13. **Performance Metrics**: Detailed query performance monitoring
14. **Usage Analytics**: Track API usage patterns and optimization opportunities
15. **Alerting**: Automated alerts for query performance issues
