#!/bin/bash

# Quarterly Payment Reports API
# New endpoints for quarterly payment reporting functionality
#
# This script demonstrates the quarterly payment reporting endpoints:
# 1. Get quarterly payment report
# 2. Get residence bill quarterly report
# 3. Execute custom SQL queries with security validation
#
# Prerequisites:
# - Payment middleware service running
# - Read-only database replica configured
# - Environment variables set (see README.md)

# Set environment variables if not already set
PMW_BASE_URL=${PMW_BASE_URL:-"http://localhost:8080"}

echo "=== Quarterly Payment Reports API Test Suite ==="
echo "Base URL: $PMW_BASE_URL"
echo ""

# Test 1: Get Quarterly Payment Report
echo "Test 1: Get quarterly payment report..."
echo "Endpoint: GET /api/reports/quarterly-payments"
echo "Description: Retrieves quarterly payment report data grouped by quarter and product type"
echo ""

curl -X GET "${PMW_BASE_URL}/api/reports/quarterly-payments" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected Response Format:"
echo '{
  "quarter": "2024-Q1",
  "productType": "Dev_Marketing", 
  "orderCount": 150,
  "totalAmount": "15000.50"
}'
echo ""

# Test 2: Get Residence Bill Quarterly Report
echo "Test 2: Get residence bill quarterly report..."
echo "Endpoint: GET /api/reports/residence-bills"
echo "Description: Retrieves residence bill quarterly report data grouped by quarter, product type, and category"
echo ""

curl -X GET "${PMW_BASE_URL}/api/reports/residence-bills" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected Response Format:"
echo '{
  "quarter": "2024-Q1",
  "productType": "Residence_Bill",
  "category": "Utilities",
  "count": 75,
  "sumAmount": "7500.25"
}'
echo ""

# Test 3: Execute Custom SQL Query - Valid Query
echo "Test 3: Execute custom SQL query (valid query)..."
echo "Endpoint: POST /api/reports/custom-query"
echo "Description: Executes a custom read-only SQL query with security validation"
echo ""

curl -X POST "${PMW_BASE_URL}/api/reports/custom-query" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "query": "SELECT product_type, COUNT(*) as order_count FROM db_prod_kip_payment_middleware.pmw_order_info WHERE created_date >= DATE_SUB(NOW(), INTERVAL 3 MONTH) GROUP BY product_type ORDER BY order_count DESC LIMIT 10"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected Response Format:"
echo '{
  "columnNames": ["product_type", "order_count"],
  "rows": [
    ["Dev_Marketing", 150],
    ["Residence_Bill", 75]
  ],
  "rowCount": 2,
  "executionTimeMs": 45
}'
echo ""

# Test 4: Execute Custom SQL Query - Aggregation Query
echo "Test 4: Execute custom SQL query (aggregation)..."
echo ""

curl -X POST "${PMW_BASE_URL}/api/reports/custom-query" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "query": "SELECT DATE_FORMAT(created_date, \"%Y-%m\") as month, SUM(CAST(order_amount AS DECIMAL(10,2))) as total_amount FROM db_prod_kip_payment_middleware.pmw_order_info WHERE created_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH) GROUP BY DATE_FORMAT(created_date, \"%Y-%m\") ORDER BY month DESC"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""

# Test 5: Execute Custom SQL Query - Invalid Query (should fail validation)
echo "Test 5: Execute custom SQL query (invalid - should fail)..."
echo "Testing security validation with INSERT statement..."
echo ""

curl -X POST "${PMW_BASE_URL}/api/reports/custom-query" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "query": "INSERT INTO pmw_order_info (order_no) VALUES (\"test\")"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 400 Bad Request with validation error"
echo ""

# Test 6: Execute Custom SQL Query - Unauthorized Table Access (should fail)
echo "Test 6: Execute custom SQL query (unauthorized table access)..."
echo "Testing access control with non-pmw table..."
echo ""

curl -X POST "${PMW_BASE_URL}/api/reports/custom-query" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "query": "SELECT * FROM mysql.user LIMIT 1"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 400 Bad Request with access control error"
echo ""

# Test 7: Execute Custom SQL Query - Complex Join Query
echo "Test 7: Execute custom SQL query (complex join)..."
echo ""

curl -X POST "${PMW_BASE_URL}/api/reports/custom-query" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "query": "SELECT o.product_type, o.category, COUNT(*) as order_count, SUM(CAST(o.order_amount AS DECIMAL(10,2))) as total_amount FROM db_prod_kip_payment_middleware.pmw_order_info o WHERE o.created_date >= DATE_SUB(NOW(), INTERVAL 1 YEAR) GROUP BY o.product_type, o.category HAVING COUNT(*) > 5 ORDER BY total_amount DESC LIMIT 20"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""

echo "=== Quarterly Payment Reports Test Suite Completed ==="

# Test Case Documentation:
#
# Business Purpose:
# These endpoints provide comprehensive reporting capabilities for the payment middleware service,
# enabling business users to generate quarterly payment reports, residence bill reports, and
# execute custom analytical queries on the payment data.
#
# Endpoint Details:
#
# 1. GET /api/reports/quarterly-payments
#    - Purpose: Generate quarterly payment aggregation reports
#    - Input: None (query parameters could be added for filtering)
#    - Output: Array of quarterly payment data with quarter, productType, orderCount, totalAmount
#    - Use Case: Business reporting, financial analysis, trend monitoring
#
# 2. GET /api/reports/residence-bills  
#    - Purpose: Generate residence bill quarterly reports
#    - Input: None (query parameters could be added for filtering)
#    - Output: Array of residence bill data with quarter, productType, category, count, sumAmount
#    - Use Case: Property management reporting, billing analysis
#
# 3. POST /api/reports/custom-query
#    - Purpose: Execute custom read-only SQL queries with security validation
#    - Input: JSON object with "query" field containing SQL SELECT statement
#    - Output: Query results with columnNames, rows, rowCount, executionTimeMs
#    - Security: Only SELECT statements allowed, table access restricted to pmw_ prefix
#    - Use Case: Ad-hoc analysis, custom reporting, data exploration
#
# Security Features:
# - Read-only database connection prevents data modification
# - SQL injection prevention through query validation
# - Table access control (only pmw_ prefixed tables)
# - Operation restrictions (only SELECT statements)
# - Query size limits and timeout protection
#
# Error Scenarios:
# - HTTP 400: Invalid query syntax, unauthorized table access, non-SELECT operations
# - HTTP 500: Database connection issues, query execution errors
# - HTTP 404: Endpoint not found (if service not properly configured)
#
# Performance Considerations:
# - Uses read-only database replica to avoid impacting main database
# - Query timeout protection prevents long-running queries
# - Connection pooling optimized for reporting workloads
# - Results may be cached for frequently requested reports
