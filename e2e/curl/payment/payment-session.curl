#!/bin/bash

# Payment Session Management API
# Core payment session endpoints from RestPaymentAPIService
#
# This script demonstrates payment session management operations:
# 1. Create payment session
# 2. Retrieve session information by ID
# 3. Create combined payment transaction
# 4. Various session scenarios and edge cases
#
# Prerequisites:
# - Payment middleware service running
# - Environment variables set (see README.md)

# Set environment variables if not already set
PMW_BASE_URL=${PMW_BASE_URL:-"http://localhost:8080"}
CONVERSATION_ID=${CONVERSATION_ID:-$(uuidgen)}
CORRELATION_ID=${CORRELATION_ID:-$(uuidgen)}

echo "=== Payment Session Management API Test Suite ==="
echo "Base URL: $PMW_BASE_URL"
echo "Conversation ID: $CONVERSATION_ID"
echo "Correlation ID: $CORRELATION_ID"
echo ""

# Test 1: Create Payment Session - Standard CRM Order
echo "Test 1: Create payment session (standard CRM order)..."
echo "Endpoint: POST /services/sessions"
echo ""

ORDER_NO="PMW$(date +%Y%m%d%H%M%S)$(openssl rand -hex 3)"
echo "Generated Order No: $ORDER_NO"

SESSION_RESPONSE=$(curl -s -X POST "${PMW_BASE_URL}/services/sessions" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "X-Conversation-ID: ${CONVERSATION_ID}" \
  -H "X-Correlation-ID: ${CORRELATION_ID}" \
  -d '{
    "orderSource": "CRM",
    "payTtl": 30,
    "currency": "CNY",
    "orderAmount": "100.00",
    "projectId": "SYYSDY",
    "category": "Dev_Marketing",
    "companyCode": "1",
    "productType": "Dev_Marketing",
    "orderNo": "'$ORDER_NO'",
    "splitPaymentToMerchants": true,
    "products": [
      {
        "unitPrice": 50.00,
        "qty": "2",
        "description": "Test Product 1"
      }
    ],
    "customerInfo": {
      "customerId": "CUST001",
      "customerName": "Test Customer",
      "email": "<EMAIL>",
      "phone": "13800138000"
    }
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n")

echo "Session Response: $SESSION_RESPONSE"

# Extract session ID for subsequent tests
if command -v jq &> /dev/null; then
  SESSION_ID=$(echo "$SESSION_RESPONSE" | jq -r '.sessionInfo.sessionId // .sessionId // empty')
  PAY_UNIT_ID=$(echo "$SESSION_RESPONSE" | jq -r '.sessionInfo.payUnitId // .payUnitId // empty')
  echo "Extracted Session ID: $SESSION_ID"
  echo "Extracted Pay Unit ID: $PAY_UNIT_ID"
else
  echo "Warning: jq not found. Please manually extract sessionId from response."
  echo "Set: export SESSION_ID='your-session-id'"
fi

echo ""

# Test 2: Retrieve Session Information
if [ ! -z "$SESSION_ID" ] && [ "$SESSION_ID" != "null" ]; then
  echo "Test 2: Retrieve session information..."
  echo "Endpoint: GET /services/session/{sessionId}"
  echo ""

  curl -X GET "${PMW_BASE_URL}/services/session/${SESSION_ID}" \
    -H "Accept: application/json" \
    -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

  echo ""
else
  echo "Test 2: Skipped - No valid session ID available"
  echo ""
fi

# Test 3: Create Payment Session - Residence Bill
echo "Test 3: Create payment session (residence bill)..."
echo ""

RESIDENCE_ORDER_NO="RES$(date +%Y%m%d%H%M%S)$(openssl rand -hex 3)"

curl -X POST "${PMW_BASE_URL}/services/sessions" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "X-Conversation-ID: $(uuidgen)" \
  -H "X-Correlation-ID: $(uuidgen)" \
  -d '{
    "orderSource": "RESIDENCE",
    "payTtl": 60,
    "currency": "CNY",
    "orderAmount": "500.00",
    "projectId": "RESIDENCE_BILLS",
    "category": "Residence_Bill",
    "companyCode": "2",
    "productType": "Residence_Bill",
    "orderNo": "'$RESIDENCE_ORDER_NO'",
    "splitPaymentToMerchants": false,
    "products": [
      {
        "unitPrice": 200.00,
        "qty": "1",
        "description": "Property Management Fee"
      },
      {
        "unitPrice": 150.00,
        "qty": "1", 
        "description": "Utilities"
      },
      {
        "unitPrice": 150.00,
        "qty": "1",
        "description": "Parking Fee"
      }
    ],
    "customerInfo": {
      "customerId": "RES001",
      "customerName": "Residence Customer",
      "email": "<EMAIL>",
      "phone": "13900139000",
      "address": "Building A, Unit 1001"
    }
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""

# Test 4: Create Combined Payment Transaction
if [ ! -z "$SESSION_ID" ] && [ "$SESSION_ID" != "null" ]; then
  echo "Test 4: Create combined payment transaction..."
  echo "Endpoint: POST /services/transactions/combined"
  echo ""

  curl -X POST "${PMW_BASE_URL}/services/transactions/combined" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "Accept-Language: zh-CN" \
    -H "X-CIP: *************" \
    -H "X-Conversation-ID: $(uuidgen)" \
    -H "X-Correlation-ID: $(uuidgen)" \
    -d '{
      "sessionId": "'$SESSION_ID'",
      "payOption": "WECHATPAY",
      "payChannel": "OFFICIAL_ACCOUNT",
      "region": "cn",
      "openId": "test_open_id_12345",
      "deviceInfo": {
        "deviceType": "mobile",
        "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)",
        "ipAddress": "*************"
      }
    }' \
    -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

  echo ""
else
  echo "Test 4: Skipped - No valid session ID available"
  echo ""
fi

# Test 5: Create Payment Session - Validation Error (Missing Required Fields)
echo "Test 5: Create payment session (validation error - missing fields)..."
echo ""

curl -X POST "${PMW_BASE_URL}/services/sessions" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "X-Conversation-ID: $(uuidgen)" \
  -H "X-Correlation-ID: $(uuidgen)" \
  -d '{
    "orderSource": "",
    "currency": "CNY",
    "orderAmount": "0.00"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 400 Bad Request with validation errors"
echo ""

# Test 6: Create Payment Session - Invalid Currency
echo "Test 6: Create payment session (invalid currency)..."
echo ""

curl -X POST "${PMW_BASE_URL}/services/sessions" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -H "X-Conversation-ID: $(uuidgen)" \
  -H "X-Correlation-ID: $(uuidgen)" \
  -d '{
    "orderSource": "CRM",
    "payTtl": 15,
    "currency": "INVALID",
    "orderAmount": "100.00",
    "projectId": "TEST",
    "category": "Test",
    "companyCode": "1",
    "productType": "Test",
    "orderNo": "TEST'$(date +%s)'",
    "products": [
      {
        "unitPrice": 100.00,
        "qty": "1",
        "description": "Test Product"
      }
    ]
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 400 Bad Request with currency validation error"
echo ""

# Test 7: Retrieve Non-existent Session
echo "Test 7: Retrieve non-existent session..."
echo ""

curl -X GET "${PMW_BASE_URL}/services/session/non-existent-session-id" \
  -H "Accept: application/json" \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 404 Not Found"
echo ""

echo "=== Payment Session Management Test Suite Completed ==="
