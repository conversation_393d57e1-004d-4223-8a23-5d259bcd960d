#!/bin/bash

# Payment Flow - WeChat Official Account Payment with Refund
# Converted from: e2e/ums/payment_flow_normal_pay_with_office_account.http
#
# This script demonstrates a complete payment flow using WeChat Pay Official Account:
# 1. Create payment session
# 2. Create payment transaction
# 3. Query order status
# 4. Process refund
# 5. Query refund status
#
# Prerequisites:
# - Payment verify component running on localhost:8081
# - Environment variables set (see README.md)

# Set environment variables if not already set
PMW_VERIFY_URL=${PMW_VERIFY_URL:-"http://localhost:8081"}
CONVERSATION_ID=${CONVERSATION_ID:-$(uuidgen)}
CORRELATION_ID=${CORRELATION_ID:-$(uuidgen)}

# Generate unique order number
ORDER_NO="SY$(uuidgen)"

echo "=== WeChat Official Account Payment Flow Test ==="
echo "Order Number: $ORDER_NO"
echo "Conversation ID: $CONVERSATION_ID"
echo "Correlation ID: $CORRELATION_ID"
echo ""

# Step 1: Create Payment Session
echo "Step 1: Creating payment session..."
SESSION_RESPONSE=$(curl -s -X GET "${PMW_VERIFY_URL}/pmw/create_session" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "orderSource": "CRM",
    "payTtl": 15,
    "currency": "CNY",
    "orderAmount": "0.01",
    "projectId": "SYYSDY",
    "category": "Dev_Marketing",
    "companyCode": "1",
    "productType": "Dev_Marketing",
    "orderNo": "'$ORDER_NO'",
    "splitPaymentToMerchants": true,
    "products": [
      {
        "unitPrice": 0.01,
        "qty": "1",
        "description": "test product1"
      }
    ]
  }')

echo "Session Response: $SESSION_RESPONSE"

# Extract session information (requires jq for JSON parsing)
if command -v jq &> /dev/null; then
  PMW_ORDER_NO=$(echo "$SESSION_RESPONSE" | jq -r '.sessionInfo.orderNo')
  PAY_UNIT_ID=$(echo "$SESSION_RESPONSE" | jq -r '.sessionInfo.payUnitId')
  SESSION_ID=$(echo "$SESSION_RESPONSE" | jq -r '.sessionInfo.sessionId')
  
  echo "Extracted - Order No: $PMW_ORDER_NO, Pay Unit ID: $PAY_UNIT_ID, Session ID: $SESSION_ID"
else
  echo "Warning: jq not found. Please manually extract sessionId, payUnitId, and orderNo from response."
  echo "Set these variables manually:"
  echo "export SESSION_ID='your-session-id'"
  echo "export PAY_UNIT_ID='your-pay-unit-id'"
  echo "export PMW_ORDER_NO='your-order-no'"
  exit 1
fi

echo ""

# Step 2: Create Payment Transaction
echo "Step 2: Creating payment transaction..."
curl -X POST "${PMW_VERIFY_URL}/pmw/create_payment_transaction?sessionId=${SESSION_ID}" \
  -H "Content-Type: application/json" \
  -d '{
    "payOption": "WECHATPAY",
    "payChannel": "OFFICIAL_ACCOUNT",
    "region": "cn",
    "openId": "43kkr3r32f9ke329iksjhe"
  }'

echo ""
echo ""

# Step 3: Query Order Status
echo "Step 3: Querying order status..."
ORDER_QUERY_RESPONSE=$(curl -s -X GET "${PMW_VERIFY_URL}/pmw/query_order?payUnitId=${PAY_UNIT_ID}&orderSource=CRM&orderNo=${PMW_ORDER_NO}")

echo "Order Query Response: $ORDER_QUERY_RESPONSE"

# Extract PSP TIN for refund
if command -v jq &> /dev/null; then
  PSP_TIN=$(echo "$ORDER_QUERY_RESPONSE" | jq -r '.pspTin')
  echo "Extracted PSP TIN: $PSP_TIN"
else
  echo "Warning: Please manually extract pspTin from response for refund operation."
  echo "Set: export PSP_TIN='your-psp-tin'"
fi

echo ""

# Step 4: Process Refund
echo "Step 4: Processing refund..."
REFUND_RESPONSE=$(curl -s -X POST "${PMW_VERIFY_URL}/pmw/refund" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": "0.01",
    "conversationId": "'$(uuidgen)'",
    "correlationId": "'$(uuidgen)'",
    "currency": "CNY",
    "notifyUrl": "http://localhost:3001/refund/callback",
    "orderNo": "'$PMW_ORDER_NO'",
    "orderSource": "CRM",
    "pspName": "ums",
    "pspTin": "'$PSP_TIN'",
    "source": "CRM",
    "sourceRefundBatchCode": "sourceRefundBatchCode-1"
  }')

echo "Refund Response: $REFUND_RESPONSE"

# Extract refund batch code
if command -v jq &> /dev/null; then
  REFUND_BATCH_CODE=$(echo "$REFUND_RESPONSE" | jq -r '.refundBatchCode')
  echo "Extracted Refund Batch Code: $REFUND_BATCH_CODE"
else
  echo "Warning: Please manually extract refundBatchCode from response."
  echo "Set: export REFUND_BATCH_CODE='your-refund-batch-code'"
fi

echo ""

# Step 5: Query Refund Status
echo "Step 5: Querying refund status..."
curl -X GET "${PMW_VERIFY_URL}/pmw/query_refund?orderNo=${PMW_ORDER_NO}&refundBatchCode=${REFUND_BATCH_CODE}&sourceRefundBatchCode=sourceRefundBatchCode-1&payUnitId=${PAY_UNIT_ID}"

echo ""
echo ""
echo "=== Payment Flow Test Completed ==="

# Test Case Documentation:
#
# Business Purpose:
# This test validates the complete WeChat Official Account payment flow including
# session creation, transaction processing, order querying, refund processing,
# and refund status verification.
#
# Input Parameters:
# - orderSource: "CRM" (string, required) - Source system identifier
# - payTtl: 15 (integer, required) - Payment time-to-live in minutes
# - currency: "CNY" (string, required) - Payment currency code
# - orderAmount: "0.01" (string, required) - Payment amount in decimal format
# - projectId: "SYYSDY" (string, required) - Project identifier
# - category: "Dev_Marketing" (string, required) - Payment category
# - companyCode: "1" (string, required) - Company identifier
# - productType: "Dev_Marketing" (string, required) - Product type classification
# - orderNo: Generated UUID (string, required) - Unique order identifier
# - splitPaymentToMerchants: true (boolean, optional) - Enable payment splitting
# - products: Array of product objects with unitPrice, qty, description
#
# Expected Response Format:
# Session Creation: HTTP 200 with sessionInfo containing sessionId, payUnitId, orderNo
# Transaction Creation: HTTP 200 with transaction details
# Order Query: HTTP 200 with order status and pspTin
# Refund: HTTP 200 with refundBatchCode
# Refund Query: HTTP 200 with refund status details
#
# Test Scenarios:
# 1. Happy Path: Complete flow with successful payment and refund
# 2. Validation Errors: Invalid orderSource, missing required fields
# 3. Authentication Failures: Missing or invalid API credentials
# 4. Edge Cases: Duplicate orderNo, invalid currency, negative amounts
#
# Prerequisites:
# - Payment verify component service running and accessible
# - Valid WeChat Pay merchant configuration
# - Database connectivity for session and transaction storage
# - Network connectivity to external PSP services
