#!/bin/bash

# Payment Query and Order Management API
# Payment order query, refund query, and cancellation endpoints
#
# This script demonstrates payment query operations:
# 1. Query order status and details
# 2. Loop query PSP order status
# 3. Query refund status and details
# 4. Cancel pending orders
# 5. Various query scenarios and edge cases
#
# Prerequisites:
# - Payment middleware service running
# - Valid payment orders and refunds in the system
# - Environment variables set (see README.md)

# Set environment variables if not already set
PMW_BASE_URL=${PMW_BASE_URL:-"http://localhost:8080"}

echo "=== Payment Query and Order Management API Test Suite ==="
echo "Base URL: $PMW_BASE_URL"
echo ""

# Test 1: Query Order Status - Standard Query
echo "Test 1: Query order status (standard query)..."
echo "Endpoint: POST /services/order/inquire"
echo ""

curl -X POST "${PMW_BASE_URL}/services/order/inquire" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "payUnitId": "PU20241201123456789",
    "orderSource": "CRM",
    "orderNo": "PMW20241201123456",
    "companyCode": "1",
    "projectId": "SYYSDY"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected Response Format:"
echo '{
  "payUnitId": "PU20241201123456789",
  "orderNo": "PMW20241201123456",
  "orderSource": "CRM",
  "orderStatus": "PAID",
  "paymentAmount": "100.00",
  "currency": "CNY",
  "pspName": "WECHATPAY",
  "pspTin": "wx_transaction_id_12345",
  "createdDate": "2024-12-01T12:34:56+08:00",
  "paidDate": "2024-12-01T12:35:30+08:00"
}'
echo ""

# Test 2: Query Order Status - Residence Bill
echo "Test 2: Query order status (residence bill)..."
echo ""

curl -X POST "${PMW_BASE_URL}/services/order/inquire" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "payUnitId": "PU20241201987654321",
    "orderSource": "RESIDENCE",
    "orderNo": "RES20241201987654",
    "companyCode": "2",
    "projectId": "RESIDENCE_BILLS"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""

# Test 3: Loop Query PSP Order Status
echo "Test 3: Loop query PSP order status..."
echo "Endpoint: POST /services/order/loop/inquire"
echo "Description: Continuously queries PSP until final status is reached"
echo ""

curl -X POST "${PMW_BASE_URL}/services/order/loop/inquire" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "payUnitId": "PU20241201123456789",
    "orderSource": "CRM",
    "orderNo": "PMW20241201123456",
    "companyCode": "1",
    "projectId": "SYYSDY",
    "maxRetries": 5,
    "retryIntervalSeconds": 2
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""

# Test 4: Query Refund Status
echo "Test 4: Query refund status..."
echo "Endpoint: POST /services/refund/inquire"
echo ""

curl -X POST "${PMW_BASE_URL}/services/refund/inquire" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "orderNo": "PMW20241201123456",
    "refundBatchCode": "RFD20241201123456789",
    "sourceRefundBatchCode": "SRC_RFD_001",
    "payUnitId": "PU20241201123456789",
    "orderSource": "CRM"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected Response Format:"
echo '{
  "refundBatchCode": "RFD20241201123456789",
  "sourceRefundBatchCode": "SRC_RFD_001",
  "orderNo": "PMW20241201123456",
  "refundAmount": "50.00",
  "currency": "CNY",
  "refundStatus": "SUCCESS",
  "pspRefundId": "wx_refund_id_12345",
  "refundDate": "2024-12-01T14:30:00+08:00",
  "reason": "Customer request"
}'
echo ""

# Test 5: Cancel Order
echo "Test 5: Cancel pending order..."
echo "Endpoint: POST /services/order/cancel"
echo ""

curl -X POST "${PMW_BASE_URL}/services/order/cancel" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "payUnitId": "PU20241201555666777",
    "orderSource": "CRM",
    "orderNo": "PMW20241201555666",
    "companyCode": "1",
    "cancelReason": "Customer cancellation request",
    "operatorId": "ADMIN001"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""

# Test 6: Query Order - Invalid Parameters
echo "Test 6: Query order (invalid parameters)..."
echo ""

curl -X POST "${PMW_BASE_URL}/services/order/inquire" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "payUnitId": "",
    "orderSource": "",
    "orderNo": ""
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 400 Bad Request with validation errors"
echo ""

# Test 7: Query Non-existent Order
echo "Test 7: Query non-existent order..."
echo ""

curl -X POST "${PMW_BASE_URL}/services/order/inquire" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "payUnitId": "NON_EXISTENT_PAY_UNIT_ID",
    "orderSource": "CRM",
    "orderNo": "NON_EXISTENT_ORDER_NO",
    "companyCode": "1",
    "projectId": "TEST"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 404 Not Found or empty result"
echo ""

# Test 8: Query Refund - Non-existent Refund
echo "Test 8: Query non-existent refund..."
echo ""

curl -X POST "${PMW_BASE_URL}/services/refund/inquire" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "orderNo": "NON_EXISTENT_ORDER",
    "refundBatchCode": "NON_EXISTENT_REFUND",
    "sourceRefundBatchCode": "NON_EXISTENT_SOURCE",
    "payUnitId": "NON_EXISTENT_PAY_UNIT",
    "orderSource": "CRM"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 404 Not Found or empty result"
echo ""

# Test 9: Cancel Already Paid Order (should fail)
echo "Test 9: Cancel already paid order (should fail)..."
echo ""

curl -X POST "${PMW_BASE_URL}/services/order/cancel" \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "payUnitId": "PU20241201123456789",
    "orderSource": "CRM",
    "orderNo": "PMW20241201123456",
    "companyCode": "1",
    "cancelReason": "Attempt to cancel paid order",
    "operatorId": "ADMIN001"
  }' \
  -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"

echo ""
echo "Expected: HTTP 400 Bad Request - Cannot cancel paid order"
echo ""

echo "=== Payment Query and Order Management Test Suite Completed ==="

# Test Case Documentation:
#
# Business Purpose:
# These endpoints provide comprehensive order and refund query capabilities,
# enabling merchants and administrators to track payment status, process
# cancellations, and monitor refund operations.
#
# Endpoint Details:
#
# 1. POST /services/order/inquire
#    - Purpose: Query order status and payment details
#    - Input: payUnitId, orderSource, orderNo, companyCode, projectId
#    - Output: Order details with payment status, amounts, PSP information
#    - Use Case: Order status tracking, customer service, reconciliation
#
# 2. POST /services/order/loop/inquire
#    - Purpose: Continuously query PSP until final payment status
#    - Input: Same as order/inquire plus retry configuration
#    - Output: Final order status after polling completion
#    - Use Case: Real-time payment confirmation, webhook alternatives
#
# 3. POST /services/refund/inquire
#    - Purpose: Query refund status and details
#    - Input: orderNo, refundBatchCode, sourceRefundBatchCode, payUnitId
#    - Output: Refund details with status, amounts, PSP refund information
#    - Use Case: Refund tracking, customer service, financial reconciliation
#
# 4. POST /services/order/cancel
#    - Purpose: Cancel pending payment orders
#    - Input: payUnitId, orderSource, orderNo, cancelReason, operatorId
#    - Output: Cancellation confirmation and updated order status
#    - Use Case: Order management, customer cancellations, inventory management
#
# Validation Rules:
# - payUnitId: Required, must be valid payment unit identifier
# - orderSource: Required, must be valid source (CRM, RESIDENCE, etc.)
# - orderNo: Required, must be unique order identifier
# - companyCode: Required for multi-tenant scenarios
# - Cancellation only allowed for pending/unpaid orders
#
# Error Scenarios:
# - HTTP 400: Invalid parameters, validation errors, business rule violations
# - HTTP 404: Order/refund not found
# - HTTP 500: Database errors, PSP communication failures
#
# Performance Considerations:
# - Loop queries have configurable retry limits and intervals
# - Database queries optimized with proper indexing
# - PSP queries may have external service latency
# - Caching may be applied for frequently queried orders
