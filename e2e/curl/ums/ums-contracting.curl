#!/bin/bash

# UMS Tenant Contracting Management
# Converted from: e2e/ums/tenant_contracting_query.http
#
# This script demonstrates UMS (Union Pay) tenant contracting operations:
# 1. Query contracting indicator for non-existent tenant
# 2. Create successful contracting
# 3. Query contracting indicator after success
# 4. Query contracting list and details
# 5. Create failed contracting
# 6. Test various contracting states
#
# Prerequisites:
# - Payment middleware service running
# - Environment variables set (see README.md)

# Set environment variables if not already set
PMW_BASE_URL=${PMW_BASE_URL:-"http://localhost:8080"}

echo "=== UMS Tenant Contracting Test Suite ==="
echo "Base URL: $PMW_BASE_URL"
echo ""

# Test 1: Query contracting indicator for non-existent tenant (should return "去签约")
echo "Test 1: Query contracting indicator for non-existent tenant..."
curl -X GET "${PMW_BASE_URL}/ums/contracting_indicator?tenantId=not_exist" \
  -H "Accept: application/json"

echo ""
echo ""

# Test 2: Create successful contracting
echo "Test 2: Creating successful contracting..."
NEW_TENANT_ID=$(uuidgen)
ACCOUNT_NO=$(uuidgen)
TENANT_NAME=$(openssl rand -hex 3)

echo "Generated - Tenant ID: $NEW_TENANT_ID, Account No: $ACCOUNT_NO, Tenant Name: $TENANT_NAME"

CONTRACTING_RESPONSE=$(curl -s -X POST "${PMW_BASE_URL}/ums/contracting" \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "'$NEW_TENANT_ID'",
    "lbsName": "杭州嘉里中心办公楼",
    "accountNo": "'$ACCOUNT_NO'",
    "lbsId": "4028e3817bf860f3017bf86279b50001",
    "brandId": "4028e3817c2b3f79017c2b48c54c0000",
    "tenantName": "'$TENANT_NAME'",
    "state": "SUCCESS"
  }')

echo "Contracting Response: $CONTRACTING_RESPONSE"

# Extract tenant ID from response
if command -v jq &> /dev/null; then
  TENANT_ID=$(echo "$CONTRACTING_RESPONSE" | jq -r '.tenantId')
  echo "Extracted Tenant ID: $TENANT_ID"
else
  TENANT_ID=$NEW_TENANT_ID
  echo "Using generated Tenant ID: $TENANT_ID"
fi

echo ""

# Test 3: Query contracting indicator after successful contracting (should return "去变更")
echo "Test 3: Query contracting indicator after successful contracting..."
curl -X GET "${PMW_BASE_URL}/ums/contracting_indicator?tenantId=${TENANT_ID}" \
  -H "Accept: application/json"

echo ""
echo ""

# Test 4: Query contracting list by brand ID
echo "Test 4: Query contracting list by brand ID..."
CONTRACTING_LIST_RESPONSE=$(curl -s -X GET "${PMW_BASE_URL}/ums/contracting?brandId=4028e3817c2b3f79017c2b48c54c0000" \
  -H "Accept: application/json")

echo "Contracting List Response: $CONTRACTING_LIST_RESPONSE"

# Extract contracting ID for detail query and cleanup
if command -v jq &> /dev/null; then
  CONTRACTING_ID=$(echo "$CONTRACTING_LIST_RESPONSE" | jq -r '.content[0].id')
  echo "Extracted Contracting ID: $CONTRACTING_ID"
else
  echo "Warning: jq not found. Please manually extract contracting ID for cleanup."
fi

echo ""

# Test 5: Query specific contracting details
echo "Test 5: Query specific contracting details..."
curl -X GET "${PMW_BASE_URL}/ums/contracting/${CONTRACTING_ID}" \
  -H "Accept: application/json"

echo ""
echo ""

# Test 6: Delete contracting record (cleanup)
echo "Test 6: Delete contracting record..."
curl -X DELETE "${PMW_BASE_URL}/ums/contracting/${CONTRACTING_ID}" \
  -H "Accept: application/json"

echo ""
echo ""

# Test 7: Create failed contracting
echo "Test 7: Creating failed contracting..."
FAILED_TENANT_ID=$(uuidgen)

FAILED_CONTRACTING_RESPONSE=$(curl -s -X POST "${PMW_BASE_URL}/ums/contracting" \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "'$FAILED_TENANT_ID'",
    "lbsName": "杭州嘉里中心办公楼",
    "lbsId": "4028e3817bf860f3017bf86279b50001",
    "brandId": "4028e3817c2b3f79017c2b48c54c0000",
    "tenantName": "",
    "state": "FAIL",
    "failReason": "资料不全，请重新提供",
    "originStatus": "",
    "rawResult": "",
    "createdDate": "2024-05-11T17:54:53+08:00",
    "lastModifiedDate": "2024-05-11T17:54:53+08:00"
  }')

echo "Failed Contracting Response: $FAILED_CONTRACTING_RESPONSE"

# Extract failed tenant ID
if command -v jq &> /dev/null; then
  FAILED_TENANT_ID_RESPONSE=$(echo "$FAILED_CONTRACTING_RESPONSE" | jq -r '.tenantId')
  echo "Failed Contracting Tenant ID: $FAILED_TENANT_ID_RESPONSE"
else
  echo "Using generated Failed Tenant ID: $FAILED_TENANT_ID"
fi

echo ""

# Test 8: Query contracting indicator for failed contracting (should return "去签约")
echo "Test 8: Query contracting indicator for failed contracting..."
curl -X GET "${PMW_BASE_URL}/ums/contracting_indicator?tenantId=${FAILED_TENANT_ID}" \
  -H "Accept: application/json"

echo ""
echo ""

# Test 9: Test CREATED state contracting
echo "Test 9: Creating CREATED state contracting..."
CREATED_TENANT_ID=$(uuidgen)

curl -X POST "${PMW_BASE_URL}/ums/contracting" \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "'$CREATED_TENANT_ID'",
    "lbsName": "杭州嘉里中心办公楼",
    "lbsId": "4028e3817bf860f3017bf86279b50002",
    "brandId": "4028e3817c2b3f79017c2b48c54c0002",
    "tenantName": "'$(openssl rand -hex 3)'",
    "state": "CREATED"
  }'

echo ""
echo ""

# Test 10: Test NOT_CONTRACTED state
echo "Test 10: Creating NOT_CONTRACTED state contracting..."
NOT_CONTRACTED_TENANT_ID=$(uuidgen)

curl -X POST "${PMW_BASE_URL}/ums/contracting" \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "'$NOT_CONTRACTED_TENANT_ID'",
    "lbsName": "杭州嘉里中心办公楼",
    "lbsId": "4028e3817bf860f3017bf86279b50002",
    "brandId": "4028e3817c2b3f79017c2b48c54c0003",
    "tenantName": "'$(openssl rand -hex 3)'",
    "state": "NOT_CONTRACTED"
  }'

echo ""
echo ""

# Test 11: Test PROCESSING state
echo "Test 11: Creating PROCESSING state contracting..."
PROCESSING_TENANT_ID=$(uuidgen)

curl -X POST "${PMW_BASE_URL}/ums/contracting" \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "'$PROCESSING_TENANT_ID'",
    "lbsName": "杭州嘉里中心办公楼",
    "lbsId": "4028e3817bf860f3017bf86279b50002",
    "brandId": "4028e3817c2b3f79017c2b48c54c0004",
    "tenantName": "'$(openssl rand -hex 3)'",
    "state": "PROCESSING"
  }'

echo ""
echo ""

echo "=== UMS Contracting Test Suite Completed ==="

# Cleanup note
echo ""
echo "Note: Some test records may need manual cleanup. Use the following commands:"
echo "curl -X DELETE \"${PMW_BASE_URL}/ums/contracting/{contracting-id}\""
