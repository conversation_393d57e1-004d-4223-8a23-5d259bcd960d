# Payment Middleware Service - Startup Troubleshooting Guide

## Current Status

✅ **Docker Services**: MySQL, Redis, and RabbitMQ are running successfully
✅ **Application Compilation**: Maven compilation completed successfully
❌ **Spring Boot Application**: Not starting properly

## Troubleshooting Steps

### 1. **Verify Docker Services**
```bash
# Check all services are running
docker-compose ps

# Expected output:
# mysql     Up      0.0.0.0:3306->3306/tcp
# redis     Up      0.0.0.0:6379->6379/tcp  
# rabbitmq  Up      0.0.0.0:5672->5672/tcp, 0.0.0.0:15672->15672/tcp
```

### 2. **Test Database Connectivity**
```bash
# Test MySQL connection
mysql -h localhost -P 3306 -u root -ptest -e "SHOW DATABASES;"

# Test Redis connection
redis-cli ping

# Expected: PONG
```

### 3. **Start Application with Detailed Logging**
```bash
# Method 1: Maven with debug logging
DEPLOY_ENV=local mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Dlogging.level.root=DEBUG"

# Method 2: Direct Java execution
DEPLOY_ENV=local java -jar target/payment-middleware-service.jar \
  --spring.docker.compose.enabled=false \
  --logging.level.root=DEBUG \
  --logging.level.com.kerryprops=TRACE

# Method 3: With specific profile
DEPLOY_ENV=local java -jar target/payment-middleware-service.jar \
  --spring.profiles.active=local \
  --spring.docker.compose.enabled=false
```

### 4. **Alternative Startup Methods**

#### Option A: Disable Docker Compose Integration
```bash
# Add to application-local.yml
spring:
  docker:
    compose:
      enabled: false
```

#### Option B: Use External Database Configuration
```bash
# Start with external database URLs
DEPLOY_ENV=local java -jar target/payment-middleware-service.jar \
  --spring.datasource.url=************************************************** \
  --spring.datasource.username=root \
  --spring.datasource.password=test \
  --spring.redis.host=localhost \
  --spring.redis.port=6379 \
  --spring.rabbitmq.host=localhost \
  --spring.rabbitmq.port=5672
```

### 5. **Database Initialization**
```bash
# Create database if it doesn't exist
mysql -h localhost -P 3306 -u root -ptest -e "CREATE DATABASE IF NOT EXISTS kip_payment_middleware;"

# Run schema initialization
mysql -h localhost -P 3306 -u root -ptest kip_payment_middleware < src/main/resources/schema.sql
```

### 6. **Port Conflict Resolution**
```bash
# Check if port 8080 is in use
lsof -i :8080

# If in use, start on different port
DEPLOY_ENV=local java -jar target/payment-middleware-service.jar --server.port=8081
```

## Quick Start Commands (Once Application is Running)

### 1. **Health Check**
```bash
curl -X GET "http://localhost:8080/actuator/health" -H "Accept: application/json"
```

### 2. **Email Status Check**
```bash
curl -X GET "http://localhost:8080/email/status" -H "Accept: application/json"
```

### 3. **Run Individual Test Suite**
```bash
# Test health endpoints
./e2e/curl/admin/admin-health.curl

# Test payment sessions
./e2e/curl/payment/payment-session.curl

# Test reporting (read-only database)
./e2e/curl/reports/quarterly-reports.curl
```

### 4. **Run Complete Test Suite**
```bash
# Local environment
./e2e/curl/run-all-tests.sh http://localhost:8080

# Development environment
./e2e/curl/run-all-tests.sh https://dev-kip-service-internal.kerryonvip.com/payment-middleware-service
```

## Expected Test Results

When the application is running properly, you should see:

### ✅ **Successful Health Check**
```json
{
  "status": "UP",
  "emailServiceStatus": "HEALTHY",
  "smtpConnectionStatus": "CONNECTED"
}
```

### ✅ **Successful Payment Session Creation**
```json
{
  "sessionId": "sess_20241201143022_abc123",
  "payUnitId": "unit_456789",
  "orderNo": "PMW20241201143022abc123",
  "status": "CREATED",
  "orderAmount": "100.00",
  "currency": "CNY"
}
```

### ✅ **Successful Quarterly Report**
```json
[
  {
    "quarter": "2024-Q4",
    "productType": "Dev_Marketing",
    "orderCount": 150,
    "totalAmount": "15000.50"
  }
]
```

## Common Issues and Solutions

### Issue 1: Docker Compose Port Mapping
**Error**: `No host port mapping found for container port 3306`
**Solution**: Restart Docker services with clean data directory

### Issue 2: Database Connection Timeout
**Error**: `Connection timeout`
**Solution**: Verify MySQL is fully initialized and accepting connections

### Issue 3: Maven Build Issues
**Error**: Compilation failures
**Solution**: Clean and rebuild: `mvn clean compile`

### Issue 4: Profile Configuration
**Error**: Wrong profile loaded
**Solution**: Explicitly set `DEPLOY_ENV=local`

## Production-Ready Testing

Once the application starts successfully, the comprehensive test suite validates:

1. **API Endpoint Coverage**: 50+ test scenarios across 11 test files
2. **Business Domain Coverage**: Payment operations, UMS integration, reporting, administration
3. **Error Handling**: Validation errors, not found scenarios, server errors
4. **Security Testing**: SQL injection prevention, table access control
5. **Performance Testing**: Response times, concurrent requests, timeout handling
6. **Database Testing**: Read-only replica usage, connection pooling
7. **Integration Testing**: PSP integration, WeChat Pay, Alipay, UMS

## Next Steps

1. **Resolve Startup Issues**: Follow troubleshooting steps above
2. **Verify Database Schema**: Ensure all required tables exist
3. **Test Individual Endpoints**: Start with health checks
4. **Run Comprehensive Test Suite**: Execute all 11 test files
5. **Validate Results**: Check HTTP status codes and response formats
6. **Performance Analysis**: Monitor response times and database queries

The test suite is production-ready and requires minimal modification once the application starts successfully.
