/***********************************************************************************************************************
 * Project - payment-middleware-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> rod peng
 * Created Date - 07/08/2021 12:28
 **********************************************************************************************************************/
package com.kerryprops.kip.pmw.data.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = {"com.kerryprops.kip.pmw.data.repository"},
        entityManagerFactoryRef = "pmwEmf",
        transactionManagerRef = "pmwJpaTransactionManager")
public class PmwDataConfig {

    @Value("${jdbc.MysqlPaymentDB.GenerateDdl:false}")
    private boolean generateDdl;

    @Value("${jdbc.MysqlPaymentDB.showSql:false}")
    private boolean showSql;

    // PersistenceExceptionTranslationPostProcessor is a bean post-processor that adds an adviser to 
    // any bean that’s annotated with @Repository so that any platform-specific exceptions are caught 
    // and then rethrown as one of Spring’s unchecked data-access exceptions.
    @Bean
    public PersistenceExceptionTranslationPostProcessor persistenceExceptionTranslationPostProcessor() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

    @Bean
    public JpaVendorAdapter jpaVendorAdapter() {
        HibernateJpaVendorAdapter adapter = new HibernateJpaVendorAdapter();
        adapter.setDatabase(Database.MYSQL);
        adapter.setShowSql(showSql);
        adapter.setGenerateDdl(generateDdl);
        adapter.setDatabasePlatform("org.hibernate.dialect.MySQLDialect");

        return adapter;
    }

    @Bean
    @Primary
    public LocalContainerEntityManagerFactoryBean pmwEmf(DataSource pmwDataSource) {
        LocalContainerEntityManagerFactoryBean localContainerEntityManagerFactoryBean
                = new LocalContainerEntityManagerFactoryBean();

        localContainerEntityManagerFactoryBean.setDataSource(pmwDataSource);
        localContainerEntityManagerFactoryBean.setPersistenceUnitName("pmwUnit");
        localContainerEntityManagerFactoryBean.setJpaVendorAdapter(jpaVendorAdapter());
        localContainerEntityManagerFactoryBean.setPackagesToScan("com.kerryprops.kip.pmw.data.entity");

        return localContainerEntityManagerFactoryBean;
    }

    @Bean
    public JpaTransactionManager pmwJpaTransactionManager(LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        JpaTransactionManager jpaTransactionManager = new JpaTransactionManager();
        jpaTransactionManager.setEntityManagerFactory(entityManagerFactory.getObject());

        return jpaTransactionManager;
    }

}
