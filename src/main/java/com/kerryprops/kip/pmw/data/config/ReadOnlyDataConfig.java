package com.kerryprops.kip.pmw.data.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * Configuration for read-only database connection for reporting features.
 * This configuration creates a separate datasource, entity manager, and transaction manager
 * specifically for read-only operations to avoid impacting the main database operations.
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = {"com.kerryprops.kip.pmw.data.repository.readonly"},
        entityManagerFactoryRef = "readOnlyEntityManagerFactory",
        transactionManagerRef = "readOnlyTransactionManager"
)
@ConditionalOnProperty(name = "pmw.readonly.enabled", havingValue = "true", matchIfMissing = true)
public class ReadOnlyDataConfig {

    @Value("${spring.datasource-readonly.url}")
    private String url;

    @Value("${spring.datasource-readonly.username}")
    private String username;

    @Value("${spring.datasource-readonly.password}")
    private String password;

    @Value("${spring.datasource-readonly.driver-class-name}")
    private String driverClassName;

    @Value("${spring.datasource-readonly.hikari.idle-timeout:600000}")
    private long idleTimeout;

    @Value("${spring.datasource-readonly.hikari.maximum-pool-size:50}")
    private int maximumPoolSize;

    @Value("${spring.datasource-readonly.hikari.minimum-idle:5}")
    private int minimumIdle;

    @Value("${spring.datasource-readonly.hikari.connection-timeout:30000}")
    private long connectionTimeout;

    @Value("${spring.datasource-readonly.hikari.connection-test-query:SELECT 1 + 1 FROM DUAL;}")
    private String connectionTestQuery;

    @Value("${jdbc.MysqlPaymentDB.showSql:false}")
    private boolean showSql;

    /**
     * Creates a read-only datasource with optimized connection pool settings for reporting queries.
     */
    @Bean(name = "readOnlyDataSource")
    public DataSource readOnlyDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName(driverClassName);
        config.setIdleTimeout(idleTimeout);
        config.setMaximumPoolSize(maximumPoolSize);
        config.setMinimumIdle(minimumIdle);
        config.setConnectionTimeout(connectionTimeout);
        config.setConnectionTestQuery(connectionTestQuery);
        config.setReadOnly(true);
        config.setPoolName("ReadOnlyHikariPool");
        
        return new HikariDataSource(config);
    }

    /**
     * JPA vendor adapter configured for MySQL with read-only optimizations.
     */
    @Bean(name = "readOnlyJpaVendorAdapter")
    public JpaVendorAdapter readOnlyJpaVendorAdapter() {
        HibernateJpaVendorAdapter adapter = new HibernateJpaVendorAdapter();
        adapter.setDatabase(Database.MYSQL);
        adapter.setShowSql(showSql);
        adapter.setGenerateDdl(false); // Never generate DDL for read-only
        adapter.setDatabasePlatform("org.hibernate.dialect.MySQLDialect");
        return adapter;
    }

    /**
     * Entity manager factory for read-only operations.
     */
    @Bean(name = "readOnlyEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean readOnlyEntityManagerFactory() {
        LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
        factory.setDataSource(readOnlyDataSource());
        factory.setPersistenceUnitName("readOnlyUnit");
        factory.setJpaVendorAdapter(readOnlyJpaVendorAdapter());
        factory.setPackagesToScan("com.kerryprops.kip.pmw.data.entity");
        
        // Additional Hibernate properties for read-only optimization
        factory.getJpaPropertyMap().put("hibernate.default_schema", "db_prod_kip_payment_middleware");
        factory.getJpaPropertyMap().put("hibernate.connection.autocommit", "true");
        factory.getJpaPropertyMap().put("hibernate.jdbc.batch_size", "50");
        factory.getJpaPropertyMap().put("hibernate.order_inserts", "false");
        factory.getJpaPropertyMap().put("hibernate.order_updates", "false");
        factory.getJpaPropertyMap().put("hibernate.jdbc.batch_versioned_data", "false");
        
        return factory;
    }

    /**
     * Transaction manager for read-only operations.
     */
    @Bean(name = "readOnlyTransactionManager")
    public JpaTransactionManager readOnlyTransactionManager() {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(readOnlyEntityManagerFactory().getObject());
        return transactionManager;
    }
}
