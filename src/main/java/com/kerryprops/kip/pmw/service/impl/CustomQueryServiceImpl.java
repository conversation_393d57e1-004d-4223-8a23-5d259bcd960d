package com.kerryprops.kip.pmw.service.impl;

import com.kerryprops.kip.pmw.data.repository.readonly.CustomQueryRepository;
import com.kerryprops.kip.pmw.service.CustomQueryService;
import com.kerryprops.kip.pmw.util.SqlQueryValidator;
import com.kerryprops.kip.pmw.webservice.resource.CustomQueryResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of CustomQueryService.
 * This service handles the business logic for executing custom SQL queries
 * with comprehensive security validation and monitoring.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomQueryServiceImpl implements CustomQueryService {

    private final CustomQueryRepository customQueryRepository;
    private final SqlQueryValidator sqlQueryValidator;

    @Override
    public CustomQueryResponse executeCustomQuery(String query) {
        log.info("Starting custom query execution with security validation");
        
        try {
            // Validate query for security compliance
            sqlQueryValidator.validateQuery(query);
            
            // Log the validated query (truncated for security)
            log.info("Executing validated custom query: {}", 
                query.length() > 200 ? query.substring(0, 200) + "..." : query);
            
            // Execute the query
            CustomQueryResponse response = customQueryRepository.executeCustomQuery(query);
            
            log.info("Successfully executed custom query. Returned {} rows in {}ms", 
                response.getRowCount(), response.getExecutionTimeMs());
            
            return response;
            
        } catch (IllegalArgumentException e) {
            log.warn("Custom query validation failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error executing custom query", e);
            throw new RuntimeException("Failed to execute custom query", e);
        }
    }
}
