package com.kerryprops.kip.pmw.webservice;

import com.kerryprops.kip.pmw.service.CustomQueryService;
import com.kerryprops.kip.pmw.webservice.resource.CustomQueryRequest;
import com.kerryprops.kip.pmw.webservice.resource.CustomQueryResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST Controller for custom SQL query execution endpoints.
 * This controller provides API endpoints for executing custom read-only SQL queries
 * with comprehensive security validation and monitoring.
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/reports", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Custom Query Executor", description = "APIs for executing custom read-only SQL queries")
public class CustomQueryController {

    private final CustomQueryService customQueryService;

    /**
     * Executes a custom SQL query and returns the results.
     * 
     * This endpoint accepts a custom SQL query, validates it for security compliance,
     * executes it on the read-only database replica, and returns the results with
     * execution metadata including column names, row data, execution time, and row count.
     *
     * @param request The custom query request containing the SQL query
     * @return CustomQueryResponse containing query results and metadata
     */
    @PostMapping("/custom-query")
    @Operation(
        summary = "Execute custom SQL query",
        description = "Executes a custom read-only SQL query with security validation. " +
                     "Only SELECT statements are allowed, and queries are validated for security compliance. " +
                     "Returns results with column names, row data, execution time, and row count."
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Successfully executed custom query",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = CustomQueryResponse.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid query or validation error",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = String.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error occurred while executing the query",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = String.class)
            )
        )
    })
    public ResponseEntity<CustomQueryResponse> executeCustomQuery(@Valid @RequestBody CustomQueryRequest request) {
        log.info("Received custom query execution request");
        
        try {
            CustomQueryResponse response = customQueryService.executeCustomQuery(request.getQuery());
            
            log.info("Successfully processed custom query request. Returned {} rows in {}ms", 
                response.getRowCount(), response.getExecutionTimeMs());
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.warn("Custom query validation failed: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(null);
        } catch (Exception e) {
            log.error("Error processing custom query request", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(null);
        }
    }
}
