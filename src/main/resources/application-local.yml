server:
  port: 8080

spring:
  application:
    name: payment-middleware-service
  datasource:
    url: ***********************************************************************************************************************************************************************************************
    username: root
    password: test
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      idle-timeout: 600000
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      connection-test-query: 'SELECT 1 + 1 FROM DUAL;'
  # Read-only replica datasource configuration (using local MySQL for development)
  datasource-readonly:
    url: *************************************************************************************************************************************************************************************************************
    username: root
    password: test
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      idle-timeout: 600000
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      connection-test-query: 'SELECT 1 + 1 FROM DUAL;'
  rabbitmq:
    password: guest
    username: guest
    addresses: localhost
    virtual-host: /
  quartz:
    properties:
      org:
        quartz:
          scheduler:
            instanceName: ${deployEnv}_PaymentScheduler
            instanceId: AUTO
            makeSchedulerThreadDaemon: true
            skipUpdateCheck: true
            batchTriggerAcquisitionMaxCount: 10
            batchTriggerAcquisitionFireAheadTimeWindow: 5000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            makeThreadsDaemons: true
          jobStore:
            misfireThreshold: 60000
            class: com.kerryprops.kip.pmw.redisjobstore.PmwRedisJobStore
            host: localhost
            port: 6379
            maxTotal: 25
            maxIdle: 5
            maxWaitMillis: 10000
            password:
            database: 1
            keyPrefix: "DEV_pmw_quartz:"
            lockTimeout: 30000
  thymeleaf:
    cache: false
    prefix: 'classpath:/velocity/'
    encoding: UTF-8
    mode: HTML
    suffix: '.html'
  data:
    redis:
      host: localhost
      port: 6379
      password:

jasypt:
  encryptor:
    password: ${ENCRYPT_KEY:kerryauthlocalpass}

cipher:
  jks:
    path: classpath:/store_client.jks
  keystore:
    password: password003
    private-key.alias-name: pmw_client
    private-key.password: password002
  v2:
    jks:
      path: classpath:/store_client.jks
    keystore:
      password: password003
      private-key.alias-name: pmw_client
      private-key.password: password002

# String keystorePath, String keystorePassword, String aliasName, String keyPassword


pmw:
  sessionCacheHour: 6
  # Enable read-only database for local testing (using local MySQL)
  readonly:
    enabled: true
  cipher:
    keystorePath: classpath:/store.jks
    keystorePassword: password003
    keyPassword: password002
    aliasName: pmw
    publicKeys:
      crm: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1B/w4mBH9hMhIe75XAYJV7djZfJfNSuQ3KiJnyN18hkN5M7DGV3WX9nJMSwoOLp1JTYRoeDnqBNuSg5/bbe/BojI0IOEUhtr45FHT5kEMmlfCUcll3+arhxdbleWbjBBRozSfouqO38PP58eqg8jBspQ65r7C4ek146LQkAzzjBxj1UtFy+yTRaPPBmzBzWorKcjaaFDTGKP+gI25F5Eu9MhJ3jNDRyd0OahJ4Htp0r8a+BagwmOaJNTAceSv8vTpoGDMaAqgRxEweCE6ebS2DBemwlYYhbjkYzwKXHL/kLowzY3Qv6IMQrbFFCHCABrVQ3v8ZDVWB4BeDCaMR31SQIDAQAB
      kip-repairing: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo5GxDtbWHPnZ9cScgJyOSaRQa6VONoGFmbQotQqzNfjJEtS6LaME/JOTjROVnWLjxWlot+5C/x4zyyoWXkht+DvW1LKFxUghR7I7gjcwCtxCFU/qMXWPTP4Z7mFH16QIVxCRyFG2OAMnqti6c7Q3oUEi8adDd6KR8ntC8fN3SmbpOUFnUtCmqoB9AwOT3iRp3klB7Bk88HfxVJt/9LbCr+qyt87eLoRJblPoebpOuCRvj2JUavtTcnks1j903gJZ19CI5iEWfxFr9nnW/g9Ptmg2lDeKHUkzTkJ43j5FriDCwPS2TXyIB/U9w3Q0rGSZPOBMGUcobwuMi/4ZaysvYwIDAQAB
      kip-order: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnZQmAb3sq5g/Fa/tQc9gByJsGahKr0Z9OtjNiM3jRuaBJfp17vWk3gZhXYbn7qWsR/JZTnsOtTz1vMYYQJBUlAeQ9SakKAQQLkoh+jD436y9aUgLdZ+aLGaDydG4sHF9mpeS2yXKsVpDlX0x+W46Pbz/deichoWQEi+Et9LcmKsxs/iFs/Db7xL+j++RMRVaEur1PKPtbTm/yPXAyZ2/RJJPKoOQjeAU8JE3HnOcT8DjgWmrs6uNku4ZbvWhpRjwhiG+ZohXnrmdqTcgBrEvJupofLHe/rPwiH7eB5wbxnyNp9moBe0QsNVqmqsuU63tV7kVGRWFOaTQy3MV9AmB0wIDAQAB
      kip-billing: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo5GxDtbWHPnZ9cScgJyOSaRQa6VONoGFmbQotQqzNfjJEtS6LaME/JOTjROVnWLjxWlot+5C/x4zyyoWXkht+DvW1LKFxUghR7I7gjcwCtxCFU/qMXWPTP4Z7mFH16QIVxCRyFG2OAMnqti6c7Q3oUEi8adDd6KR8ntC8fN3SmbpOUFnUtCmqoB9AwOT3iRp3klB7Bk88HfxVJt/9LbCr+qyt87eLoRJblPoebpOuCRvj2JUavtTcnks1j903gJZ19CI5iEWfxFr9nnW/g9Ptmg2lDeKHUkzTkJ43j5FriDCwPS2TXyIB/U9w3Q0rGSZPOBMGUcobwuMi/4ZaysvYwIDAQAB
      KIP_POINTS: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxN3YkUE3QpBqoGbU3t1L0wHzKk+KrvEOi+dgR9ynRfMqepo7ZscKay3yW1cG0/26cs+G2Xglwdsk5hA4cbsI0e7TJnQ/JqGskmfpggcJWMWtH1cAFMBT/MungrykRR5/QVpnI3ZQ8Fb0/xB1CGbhHGMCGVOUU5fHXEsJRqwTtCh8wlwXVm81KXk/NFd2PjTmNL5Pby2YvvsMuq7m1iT6xRJX1WSchEBJd3pR4Bq1pDiFPDwgQm5uyynNql4zCyd8bIdqVmtyEdbyJASR6s+wncH/QrSXPDyTz598ZTwErh/S3OciCmgipbxArwMTQIidZ2AMm6By1LjcZEkLbQyADwIDAQAB
      KIP_CAR_PARKING: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxiD1HIbLjNHoaxTlOBVFbUk6VPVbxmojHI0FGhYgiRXzKcVTDmrLy8a508ZE83PtiQJTWqNXW2a/qP3JplrPBW/NwwltiMhqOGNQIWBxnv3fHKAgSxbZT1Ex1zrLrFvfItRzU5Uz4bHLHRQGPC2msDXy0ZKrRpJdhIBwWbGqXxQ/ToI1kbQ7nqENb/DeIQqLS8/yuAvXe6gPIyAusHVrh7LLtOd3rICIYrEiXp5fkrV0dr7n9/4PZ28Wxznpzr8L7LZay7Ye4y+naINrXDhD6s6OYTY19WjSCC5u2qM/QpGlg8jZXdvM1010rljnmdxEoNigMi7bmXHEaBepkjinkwIDAQAB
      KIP_ITEM_LEASING: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApTpZJ8ugOzEKx70U1mT1YcY9QbTCIWn5qrIW/wUGBJkfAR+asZtETSm66myf78OCwG5Kaf9EJ2OJLNIsJPM7N1bQNyBa/EQhTHKMOhPvgRymgObpaqH1j/hxzx3MFJfpm9x59vT1shxv1A5+vbOvSwXD06GrGNy0PBdJhG2pixGJVI7at60CX0u23F2tDBlCJysS2zxY4OYZ6BIRTpsOiBXb/xyDcd0cDT/8eVLrCVgjUJJ3AZrh0NC2LoBFIkllvXh/L8Vnstuo7jlkYemGpviIMhA6p7fPbwixfFXh2pQ4j15oTlv67NixFUsqz33Z2QzA2ehTE0Sld0Er9Z950wIDAQAB
      KIP_CUSTOMIZED_PROCESS: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvtlUuoX8AxwKyFCaIXsfhfClvlJwajSR6IwGfsvm1vQKL9JD+DqCasWuyI278Aq558ULcrR9SsJWGAgvOoeiJzuvtdq0fWnux/V8ZmGe3S+iln5Ywzy8ugI91TgfE90Nt0VFWfNE+/lblrSpx+nTrdbej2LSM9aB8xO4Wahx4L2uNgV9+Nkw5bTXQ73ivSVeWpPZtoyY8vfb00M61wWa5nieNyM9lrB+Lzo+aYY90BE8lp/8/3yLnmYyf+9jQI2wHej3D/WKARRPddTA/bwx2eIkVpLED47alNMqVcINpHvFQtT5AparveTL1o153vZjotweb5VYGgDjccx1sxIWgQIDAQAB
      KERRY_PAY: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs+vEdvGn5Hexu393+wCxEBXf8Try5Do2JlVDPvJ6IzGoYDOyxIXLo+pqZoWorkLL9CwDqQr9+hO/2UCMuZypI6tFPIexip/QL+ZsqX2P+ZHWd8gibSoQg40imHFaYB/xA9LJGDl2mYU9W9iuMQcSD8fDAtCOjEAHiCFMMfv2GjgsAWrdHbEpTsXE3xymu4Ke1v3wCdxuE7xWIZ4WwRs4fWFvIvTwyjAHfVU3erWoEJsDhee3Q5/eLRIhi3+uD+iHh83mmR8/O2gx+C1FIuSHHIDEMHSmEbg3yaY9JG37REDfBf2CximXC6j8+jwSGqRGMiqmQi+i2nb4gC8tLrXhEwIDAQAB
  wxpay:
    host: https://api.mch.weixin.qq.com
    lifeHost: https://payapp.wechatpay.cn
    spMerchantAccount: **********
    bizCircleMerchantAccount: **********
    spAppId: wxb81a622ed6d60adf
    merchantAccounts:
      **********:
        keystorePath: classpath:/apiclient_key_partner.pem
        merchantSerialNumber: "7886BB4A3148C2589086C08F7EAEA213FFAE9061"
        apiV3Key: "jsdkjkfkjsd89989llsdkkkshdnmwydk"
        apiV2Key: "jsdkjkfkjsd89989llsdkkkshdnmwyv2"
        name: "嘉里(中国)项目管理有限公司上海分公司"
    #      **********:
    #        merchantSerialNumber: "6200A562B5A478106FD8B1B3F4DBE5586A87F926"
    #        name: "嘉里(中国)项目管理有限公司上海分公司"
    #        keystorePath: classpath:/apiclient_**********_key.pem
    #        apiV3Key: "89llsdkkkshdnmwydkjsdkjkfkjsd899"
    accountMappings:
      - companyCode: 41009 # 沈阳雅颂大苑
        Dev_Marketing: **********
        Car_Parking: **********
        Monthly_Parking: **********
        projectId: 'SYYSDY'
      - companyCode: 41009 # 沈阳雅颂阁
        Dev_Marketing: **********
        Car_Parking: **********
        projectId: 'SYYSG'
      - companyCode: 41009 # 沈阳雅颂居
        Dev_Marketing: **********
        Car_Parking: **********
        projectId: 'SYYSJ'
      - companyCode: 31007 # BKC
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 31017 # HKC
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 31006 # JAKC
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 33015 # KP
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 32001 # KP
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 31038 # 寰安置业
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 41022 # QHKC 前海物业
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 43028 # QHKC 前海物业(新CO)
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 32002 # TKC天津嘉里
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 31019 # SKC沈阳嘉里
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 31003 # JKC新慈厚
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 42005
        Dev_Marketing: **********
        Car_Parking: **********
      - companyCode: 42007
        Dev_Marketing: **********
        Car_Parking: **********
    wx-life:
      - companyCode: 42007 # 华庭二期
        merchantAccount: **********
        institution-id: ************
        city-id: 310000
        projectId: "190"
        paymentItemIds:
          APP: "**************"
          MINI_PROGRAM: "**************"
      - companyCode: 41022 # 前海
        merchantAccount: **********
        institution-id: *************
        city-id: 440300
        projectId: "192"
        paymentItemIds:
          APP: "**************"
          MINI_PROGRAM: "**************"
      - companyCode: 41028 # 榕顺：灏云峯
        merchantAccount: **********
        institution-id: ************
        city-id: 350100
        projectId: "FZHYF"
        paymentItemIds:
          OFFICIAL_ACCOUNT: **************
      - companyCode: 41029 # 榕泽：江南岸
        merchantAccount: **********
        institution-id: ************
        city-id: 350100
        projectId: "FZJLZX"
        paymentItemIds:
          APP: "**************"
          MINI_PROGRAM: "**************"
      - companyCode: 41030 # 郑州
        merchantAccount: **********
        institution-id: ************
        city-id: 410100
        projectId: "HNZZ"
        paymentItemIds:
          APP: "**************"
          MINI_PROGRAM: "**************"
      - companyCode: 43025 # 企业公馆
        merchantAccount: **********
        institution-id: **************
        city-id: 310000
        projectId: "181"
        paymentItemIds:
          MINI_PROGRAM: "****************"
      - companyCode: 41009 # 沈阳雅颂大苑
        merchantAccount: **********
        institution-id: **************
        city-id: 110000
        projectId: "SYYSDY"
        paymentItemIds:
          MINI_PROGRAM: "****************"
      - companyCode: 41009 # 沈阳雅颂居
        merchantAccount: **********
        institution-id: **************
        city-id: 110000
        projectId: "SYYSJ"
        paymentItemIds:
          MINI_PROGRAM: "****************"
      - companyCode: 41009 # 沈阳雅颂阁
        merchantAccount: **********
        institution-id: **************
        city-id: 110000
        projectId: "SYYSG"
        paymentItemIds:
          MINI_PROGRAM: "****************"
  alipay:
    sign_type: RSA2
    alipayHost: https://openapi.alipay.com/gateway.do?charset=utf-8
    spMerchantAccount: '****************' #服务商开放平台第三方应用appId
    #supportedProjects: 192,
    #supportedProjects: 1222, 192, BKC, HKC, JAKC, KEC1, KEC3, PKC, SZKP, TKC, ZYJ, ZYJXQV2
    supportedProjects: 184, 192, BKC, HKC, JAKC, KEC1, KEC3, SKC, SYYSDY, SYYSJ, SZXP, TJYSJ, TKC
    withholdingProjects: 184, 192, HKC, JAKC, KEC1, KEC3, SKC, SYYSDY, SYYSJ, SZXP, TJYSJ, TKC
    merchantAccounts:
      ****************:
        keystorePath: classpath:/alipay_****************_key.pem
        merchantSerialNumber: "7886BB4A3148C2589086C08F7EAEA213FFAE9061"
        publicKeysPkcs8: "****************^MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA10qN8Xmpt+QTy4fkrTCi2/bOhKEyPYc5LV2dfwyDSLQrKDUZ1rKktQrJHMftMo+ZF77GWRsS67v7M9WB/M+CXbQcIpBPtfppnXswTZwlfqeMqh+kuVFNTp6zpFAWvdGrLf8dAaDZGe231fSiiQXJBTvVxtbglyqAID8RoIfwFgBQxTdIZ+VYomBm8hn//qDVI1DGZr+KyMec56aNvoP0j5FjhDfpLPObAXRLJfqWQXTRRR78PcLvO6udSx9G1J+0kD4JXo0Cmy2ULmRzUoo9rqwl4rgICd2yGjKgwIL2DiMdNlnfBL2ZNxLS6p+7llVlI4ySz8JcEcrEg3ggXufGywIDAQAB"
        name: "嘉里(中国)项目管理有限公司上海分公司"
      ****************:
        appAuthToken: '202207BB89591baa9d364bc4b2213565ce1cdE37' # app_auth_code
      ****************:
        appAuthToken: '202204BBf0d49d82e1234335b9a4763594c50X84'
    accountMappings:
      - companyCode: 41009 # 沈阳雅颂大苑
        Dev_Marketing: ****************
        Car_Parking: ****************
        projectId: 'SYYSDY'
      - companyCode: 41009 # 沈阳雅颂阁
        Dev_Marketing: ****************
        Car_Parking: ****************
        projectId: 'SYYSG'
      - companyCode: 41009 # 沈阳雅颂居
        Dev_Marketing: ****************
        Car_Parking: ****************
        projectId: 'SYYSJ'
      - companyCode: 31007 # ?
        Dev_Marketing: '****************' # appId 寰安 前海嘉里
        Car_Parking: '****************'
      - companyCode: 31017 # ?
        Dev_Marketing: '****************' # appId 杭州嘉里
        Car_Parking: '****************'
      - companyCode: 41009 # 沈阳雅颂大苑
        Dev_Marketing: ****************
        Car_Parking: ****************
        projectId: 'SYYSDY'
      - companyCode: 41009 # 沈阳雅颂阁
        Dev_Marketing: ****************
        Car_Parking: ****************
        projectId: 'SYYSG'
      - companyCode: 41009 # 沈阳雅颂居
        Dev_Marketing: ****************
        Car_Parking: ****************
        projectId: 'SYYSJ'
  china-ums:
    appId: f0ec96ad2c3848b5b810e7aadf369e2f
    appKey: 775481e2556e4564985f5439a5e6a277
    vaMerchantNo: 898310060514001
    vaTermNo: "00000001"
    msgSrcId: 3199
    newShopFlag: GWCXS
    #银联【测试】订单前缀
    orderPrefix: 3199
    #银联【生产】订单前缀
    # orderPrefix: 14BF
    # salt: AKciBWatZAHK38BRBh4ZWJFHzeRGacaDsX4PXrKBQ8XpahW2
    salt: 4TrwkyXbzNphbZbd3eDYPpmaFAznYZ7Ef7RHRrK4ZCS3Yew5
    url: https://test-api-open.chinaums.com

    #以下全部为UMS在线签约配置
    contractingId: 898310153110001
    contractingKey: udik876ehjde32dU61edsxsf
    startContractingUrl:
      PC: https://selfapply-test.chinaums.com/self-sign-web/#/verify
      H5: https://selfapply-test.chinaums.com/self-sign-mobile/#/chooseRole
    updateContractingUrl:
      PC: https://selfapply-test.chinaums.com/self-sign-web/#/alterHome
      H5: https://selfapply-test.chinaums.com/self-sign-mobile/#/chooseChangeType
client:
  cipher:
    jks.path: classpath:/store_client.jks
    keystore:
      password: password003
      private-key.alias-name: pmw_client
      private-key.password: password002
    public_key_pkcs8:
      webcashier: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuRKdrvmS/1FdSvzljmq8jNEAPMLLxuTT+yCAw5gZ5VaXQ6BR1LudV2N6YOFYp7yUoOye3CobHI9GhAXrNaHrS3XoMk7/49g/uNNjJUTPzQ1xXZRbTnRgOkAbstYZDWY53S8X8fqk6ET0h3q2VwTuvkNkzZpiswkSFi7/SqLF0P+DEewyDTeywaGTUO4ls+8nTtl+T63LRSBGd8qUdOzgnfhr/YosKg3ePFyw1uC2UCK65KG47kmext+rLGFXT1o8oZ/Mlw5e+0aVSEoa6+MGvXmhdV0IURnw/DJlalisxppFDHjyg6amBqC08w7r1nooqZvnjCXiXR+LEPlfj6hm1wIDAQAB
      kip: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuRKdrvmS/1FdSvzljmq8jNEAPMLLxuTT+yCAw5gZ5VaXQ6BR1LudV2N6YOFYp7yUoOye3CobHI9GhAXrNaHrS3XoMk7/49g/uNNjJUTPzQ1xXZRbTnRgOkAbstYZDWY53S8X8fqk6ET0h3q2VwTuvkNkzZpiswkSFi7/SqLF0P+DEewyDTeywaGTUO4ls+8nTtl+T63LRSBGd8qUdOzgnfhr/YosKg3ePFyw1uC2UCK65KG47kmext+rLGFXT1o8oZ/Mlw5e+0aVSEoa6+MGvXmhdV0IURnw/DJlalisxppFDHjyg6amBqC08w7r1nooqZvnjCXiXR+LEPlfj6hm1wIDAQAB
  read:
    timeout: 2000
  connection:
    timeout: 2000
    ssl:
      validation:
        service: true
        payeco: true
        alipay: true
        wechatpay: true
        hostname:
          skipverify: true

#### wechatpay adapter cipher configuration
wechatpay:
  client.sign_type: RSA
  host: https://api.mch.weixin.qq.com
  kip_repairing:
    md5_key: 12345
    hmacSha256_key:
    cipher:
      p12:
        path: classpath:/wechatpay_cert.ticketing.p12
      keystore:
        password: 12345
  kip_order:
    md5_key: 12345
    hmacSha256_key:
    cipher:
      p12:
        path: classpath:/wechatpay_cert.ticketing.p12
      keystore:
        password: 12345
  crm:
    md5_key: 12345
    hmacSha256_key:
    cipher:
      p12:
        path: classpath:/wechatpay_cert.ticketing.p12
      keystore:
        password: 12345
  kip_billing:
    md5_key: 12345
    hmacSha256_key:
    cipher:
      p12:
        path: classpath:/wechatpay_cert.ticketing.p12
      keystore:
        password: 12345
  webcashier:
    host: https://latest.www:abcd:com/webcashier
  client:
    redirect_http_method: POST
#### alipayV2 adapter cipher configuration
alipay:
  v2:
    jks:
      path: classpath:/store_client.jks
    keystore:
      password: password003
      private-key.alias-name: pmw_client
      private-key.password: password002

deployEnv: DEV

# Temporarily disable read-only database for local testing

#### email configuration
email:
  smtp:
    monitor_group: <EMAIL>
    biz_group: <EMAIL>

#### Tridion configuration
tridion:
  pmw:
    en_cn: classpath:/tridion.pmw.en_cn.properties
    zh_cn_cn: classpath:/tridion.pmw.zh_cn_cn.properties
    installment:
      en_cn: classpath:/tridion.pmw.installment.en_cn.properties
      zh_cn_cn: classpath:/tridion.pmw.installment.zh_cn_cn.properties

service:
  pmw:
    hostname: http://localhost:8080/services
    #vip: https://payment.kerryonvip.com/payment-middleware-service/services
    vip: https://dev-payment.kerryonvip.com/services
    vipSL: "https://sl-payment.kerryonvip.com/services"
  unified-messaging-service: https://dev-kip-service-internal.kerryonvip.com/unified-messaging-service
  prod-live-flag-service: 'https://prod-live-flag-internal.kerryplus.com'
  #  billingCallback: 'https://dev-kip-service-internal.kerryonvip.com/kip-billing/s/apt/bill/direct_debits/agreement/%s/callback'
  billingCallback: 'http://localhost:8082/s/apt/bill/direct_debits/agreement/%s/callback'
  points-service:
    base: 'https://dev-kip-service-internal.kerryonvip.com/points-service'
    membership-activated: '${service.points-service.base}/wechat_pay/memberPointsAuth'
    payment-notify: '${service.points-service.base}/wechat_pay/payment'
    refund-notify: '${service.points-service.base}/wechat_pay/refund'
    card-notify: '${service.points-service.base}/wechat_pay/member_card_open'
  billing-service:
    #    base: https://dev-kip-service-internal.kerryonvip.com/kip-billing
    base: http://localhost:8082
    debit-pre-verify: ${service.billing-service.base}/s/apt/direct_debits/psp/wechatpay/agreements/pre_verify
    confirm-agreement: ${service.billing-service.base}/s/apt/direct_debits/psp/wechatpay/agreements/confirm

management:
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: /actuator
  endpoint:
    health:
      show-details: always

feign:
  client:
    config:
      prodLiveFlagService:
        connectTimeout: 1500 #单位毫秒
        readTimeout: 1500 #单位毫秒