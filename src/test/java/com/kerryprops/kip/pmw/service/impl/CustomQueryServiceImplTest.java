package com.kerryprops.kip.pmw.service.impl;

import com.kerryprops.kip.pmw.data.repository.readonly.CustomQueryRepository;
import com.kerryprops.kip.pmw.util.SqlQueryValidator;
import com.kerryprops.kip.pmw.webservice.resource.CustomQueryResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CustomQueryServiceImpl.
 */
@ExtendWith(MockitoExtension.class)
class CustomQueryServiceImplTest {

    @Mock
    private CustomQueryRepository customQueryRepository;

    @Mock
    private SqlQueryValidator sqlQueryValidator;

    @InjectMocks
    private CustomQueryServiceImpl customQueryService;

    private CustomQueryResponse mockResponse;

    @BeforeEach
    void setUp() {
        List<String> columns = Arrays.asList("product_type", "count");
        List<List<Object>> rows = Arrays.asList(
            Arrays.asList("residential", 150),
            Arrays.asList("commercial", 75)
        );
        mockResponse = new CustomQueryResponse(columns, rows, 245L, 2);
    }

    @Test
    void executeCustomQuery_ShouldReturnResponse_WhenValidQuery() {
        // Given
        String validQuery = "SELECT product_type, COUNT(*) FROM pmw_order_info GROUP BY product_type";
        doNothing().when(sqlQueryValidator).validateQuery(validQuery);
        when(customQueryRepository.executeCustomQuery(validQuery)).thenReturn(mockResponse);

        // When
        CustomQueryResponse result = customQueryService.executeCustomQuery(validQuery);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getColumns()).hasSize(2);
        assertThat(result.getColumns()).containsExactly("product_type", "count");
        assertThat(result.getRows()).hasSize(2);
        assertThat(result.getRows().get(0)).containsExactly("residential", 150);
        assertThat(result.getRows().get(1)).containsExactly("commercial", 75);
        assertThat(result.getExecutionTimeMs()).isEqualTo(245L);
        assertThat(result.getRowCount()).isEqualTo(2);

        verify(sqlQueryValidator).validateQuery(validQuery);
        verify(customQueryRepository).executeCustomQuery(validQuery);
    }

    @Test
    void executeCustomQuery_ShouldThrowIllegalArgumentException_WhenValidationFails() {
        // Given
        String invalidQuery = "DELETE FROM pmw_order_info";
        doThrow(new IllegalArgumentException("Forbidden keyword detected: DELETE"))
            .when(sqlQueryValidator).validateQuery(invalidQuery);

        // When & Then
        assertThatThrownBy(() -> customQueryService.executeCustomQuery(invalidQuery))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Forbidden keyword detected: DELETE");

        verify(sqlQueryValidator).validateQuery(invalidQuery);
        verify(customQueryRepository, never()).executeCustomQuery(anyString());
    }

    @Test
    void executeCustomQuery_ShouldThrowRuntimeException_WhenRepositoryThrowsException() {
        // Given
        String validQuery = "SELECT * FROM pmw_order_info";
        doNothing().when(sqlQueryValidator).validateQuery(validQuery);
        when(customQueryRepository.executeCustomQuery(validQuery))
            .thenThrow(new RuntimeException("Database connection error"));

        // When & Then
        assertThatThrownBy(() -> customQueryService.executeCustomQuery(validQuery))
            .isInstanceOf(RuntimeException.class)
            .hasMessage("Failed to execute custom query");

        verify(sqlQueryValidator).validateQuery(validQuery);
        verify(customQueryRepository).executeCustomQuery(validQuery);
    }

    @Test
    void executeCustomQuery_ShouldHandleEmptyResults_WhenNoDataReturned() {
        // Given
        String validQuery = "SELECT * FROM pmw_order_info WHERE 1=0";
        List<String> columns = Arrays.asList("id", "product_type");
        List<List<Object>> emptyRows = Arrays.asList();
        CustomQueryResponse emptyResponse = new CustomQueryResponse(columns, emptyRows, 50L, 0);
        
        doNothing().when(sqlQueryValidator).validateQuery(validQuery);
        when(customQueryRepository.executeCustomQuery(validQuery)).thenReturn(emptyResponse);

        // When
        CustomQueryResponse result = customQueryService.executeCustomQuery(validQuery);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getColumns()).hasSize(2);
        assertThat(result.getRows()).isEmpty();
        assertThat(result.getExecutionTimeMs()).isEqualTo(50L);
        assertThat(result.getRowCount()).isEqualTo(0);

        verify(sqlQueryValidator).validateQuery(validQuery);
        verify(customQueryRepository).executeCustomQuery(validQuery);
    }

    @Test
    void executeCustomQuery_ShouldLogQueryExecution_WhenSuccessful() {
        // Given
        String validQuery = "SELECT COUNT(*) FROM pmw_order_info";
        doNothing().when(sqlQueryValidator).validateQuery(validQuery);
        when(customQueryRepository.executeCustomQuery(validQuery)).thenReturn(mockResponse);

        // When
        CustomQueryResponse result = customQueryService.executeCustomQuery(validQuery);

        // Then
        assertThat(result).isNotNull();
        verify(sqlQueryValidator).validateQuery(validQuery);
        verify(customQueryRepository).executeCustomQuery(validQuery);
    }
}
