package com.kerryprops.kip.pmw.service.impl;

import com.kerryprops.kip.pmw.data.dto.ResidenceBillReportDto;
import com.kerryprops.kip.pmw.data.repository.readonly.ResidenceBillReportRepository;
import com.kerryprops.kip.pmw.webservice.resource.ResidenceBillReportResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

/**
 * Unit tests for ResidenceBillReportServiceImpl.
 */
@ExtendWith(MockitoExtension.class)
class ResidenceBillReportServiceImplTest {

    @Mock
    private ResidenceBillReportRepository residenceBillReportRepository;

    @InjectMocks
    private ResidenceBillReportServiceImpl residenceBillReportService;

    private List<ResidenceBillReportDto> mockReportData;

    @BeforeEach
    void setUp() {
        mockReportData = Arrays.asList(
            new ResidenceBillReportDto("2024Q1", "residential", "maintenance_fee", 150L, new BigDecimal("45000.00")),
            new ResidenceBillReportDto("2024Q1", "commercial", "parking_fee", 75L, new BigDecimal("22500.00")),
            new ResidenceBillReportDto("2024Q2", "residential", "utility_fee", 200L, new BigDecimal("60000.00"))
        );
    }

    @Test
    void generateResidenceBillQuarterlyReport_ShouldReturnConvertedResponses_WhenDataExists() {
        // Given
        when(residenceBillReportRepository.getResidenceBillQuarterlyReport()).thenReturn(mockReportData);

        // When
        List<ResidenceBillReportResponse> result = residenceBillReportService.generateResidenceBillQuarterlyReport();

        // Then
        assertThat(result).hasSize(3);
        
        ResidenceBillReportResponse firstResponse = result.get(0);
        assertThat(firstResponse.getQuarter()).isEqualTo("2024Q1");
        assertThat(firstResponse.getProductType()).isEqualTo("residential");
        assertThat(firstResponse.getCategory()).isEqualTo("maintenance_fee");
        assertThat(firstResponse.getCount()).isEqualTo(150L);
        assertThat(firstResponse.getSumAmount()).isEqualTo(new BigDecimal("45000.00"));
        
        ResidenceBillReportResponse secondResponse = result.get(1);
        assertThat(secondResponse.getQuarter()).isEqualTo("2024Q1");
        assertThat(secondResponse.getProductType()).isEqualTo("commercial");
        assertThat(secondResponse.getCategory()).isEqualTo("parking_fee");
        assertThat(secondResponse.getCount()).isEqualTo(75L);
        assertThat(secondResponse.getSumAmount()).isEqualTo(new BigDecimal("22500.00"));
        
        ResidenceBillReportResponse thirdResponse = result.get(2);
        assertThat(thirdResponse.getQuarter()).isEqualTo("2024Q2");
        assertThat(thirdResponse.getProductType()).isEqualTo("residential");
        assertThat(thirdResponse.getCategory()).isEqualTo("utility_fee");
        assertThat(thirdResponse.getCount()).isEqualTo(200L);
        assertThat(thirdResponse.getSumAmount()).isEqualTo(new BigDecimal("60000.00"));
    }

    @Test
    void generateResidenceBillQuarterlyReport_ShouldReturnEmptyList_WhenNoDataExists() {
        // Given
        when(residenceBillReportRepository.getResidenceBillQuarterlyReport()).thenReturn(Arrays.asList());

        // When
        List<ResidenceBillReportResponse> result = residenceBillReportService.generateResidenceBillQuarterlyReport();

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void generateResidenceBillQuarterlyReport_ShouldThrowRuntimeException_WhenRepositoryThrowsException() {
        // Given
        when(residenceBillReportRepository.getResidenceBillQuarterlyReport())
            .thenThrow(new RuntimeException("Database connection error"));

        // When & Then
        assertThatThrownBy(() -> residenceBillReportService.generateResidenceBillQuarterlyReport())
            .isInstanceOf(RuntimeException.class)
            .hasMessage("Failed to generate residence bill quarterly report");
    }

    @Test
    void generateResidenceBillQuarterlyReport_ShouldHandleNullValues_WhenDataContainsNulls() {
        // Given
        List<ResidenceBillReportDto> dataWithNulls = Arrays.asList(
            new ResidenceBillReportDto("2024Q1", null, null, null, null),
            new ResidenceBillReportDto(null, "residential", "maintenance_fee", 0L, BigDecimal.ZERO)
        );
        when(residenceBillReportRepository.getResidenceBillQuarterlyReport()).thenReturn(dataWithNulls);

        // When
        List<ResidenceBillReportResponse> result = residenceBillReportService.generateResidenceBillQuarterlyReport();

        // Then
        assertThat(result).hasSize(2);
        
        ResidenceBillReportResponse firstResponse = result.get(0);
        assertThat(firstResponse.getQuarter()).isEqualTo("2024Q1");
        assertThat(firstResponse.getProductType()).isNull();
        assertThat(firstResponse.getCategory()).isNull();
        assertThat(firstResponse.getCount()).isNull();
        assertThat(firstResponse.getSumAmount()).isNull();
        
        ResidenceBillReportResponse secondResponse = result.get(1);
        assertThat(secondResponse.getQuarter()).isNull();
        assertThat(secondResponse.getProductType()).isEqualTo("residential");
        assertThat(secondResponse.getCategory()).isEqualTo("maintenance_fee");
        assertThat(secondResponse.getCount()).isEqualTo(0L);
        assertThat(secondResponse.getSumAmount()).isEqualTo(BigDecimal.ZERO);
    }
}
