package com.kerryprops.kip.pmw.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Unit tests for SqlQueryValidator.
 */
class SqlQueryValidatorTest {

    private SqlQueryValidator sqlQueryValidator;

    @BeforeEach
    void setUp() {
        sqlQueryValidator = new SqlQueryValidator();
    }

    @Test
    void validateQuery_ShouldPass_WhenValidSelectQuery() {
        // Given
        String validQuery = "SELECT product_type, COUNT(*) FROM db_prod_kip_payment_middleware.pmw_order_info GROUP BY product_type";

        // When & Then
        assertThatNoException().isThrownBy(() -> sqlQueryValidator.validateQuery(validQuery));
    }

    @Test
    void validateQuery_ShouldPass_WhenSelectQueryWithPmwPrefix() {
        // Given
        String validQuery = "SELECT * FROM pmw_confirm_info WHERE created_date > '2024-01-01'";

        // When & Then
        assertThatNoException().isThrownBy(() -> sqlQueryValidator.validateQuery(validQuery));
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryIsNull() {
        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(null))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Query cannot be null or empty");
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryIsEmpty() {
        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(""))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Query cannot be null or empty");
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryIsNotSelect() {
        // Given
        String invalidQuery = "INSERT INTO pmw_order_info (product_type) VALUES ('test')";

        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(invalidQuery))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Only SELECT statements are allowed");
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryContainsUpdate() {
        // Given
        String invalidQuery = "SELECT * FROM pmw_order_info; UPDATE pmw_order_info SET product_type = 'test'";

        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(invalidQuery))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Forbidden keyword detected: UPDATE");
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryContainsDelete() {
        // Given
        String invalidQuery = "SELECT * FROM pmw_order_info WHERE id IN (SELECT id FROM pmw_order_info) DELETE FROM pmw_order_info";

        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(invalidQuery))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Forbidden keyword detected: DELETE");
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryContainsDrop() {
        // Given
        String invalidQuery = "SELECT * FROM pmw_order_info; DROP TABLE pmw_order_info";

        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(invalidQuery))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Forbidden keyword detected: DROP");
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryContainsMultipleStatements() {
        // Given
        String invalidQuery = "SELECT * FROM pmw_order_info; SELECT * FROM pmw_confirm_info";

        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(invalidQuery))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Query contains potentially dangerous patterns");
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryContainsSqlComments() {
        // Given
        String invalidQuery = "SELECT * FROM pmw_order_info -- WHERE id = 1";

        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(invalidQuery))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Query contains potentially dangerous patterns");
    }

    @Test
    void validateQuery_ShouldPass_WhenQueryHasTrailingSemicolon() {
        // Given
        String validQuery = "SELECT * FROM pmw_order_info WHERE product_type = 'residential';";

        // When & Then
        assertThatNoException().isThrownBy(() -> sqlQueryValidator.validateQuery(validQuery));
    }

    @Test
    void validateQuery_ShouldThrowException_WhenQueryContainsExec() {
        // Given
        String invalidQuery = "SELECT * FROM pmw_order_info; EXEC sp_test";

        // When & Then
        assertThatThrownBy(() -> sqlQueryValidator.validateQuery(invalidQuery))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Forbidden keyword detected: EXEC");
    }

    @Test
    void isSimpleSelectQuery_ShouldReturnTrue_WhenSimpleSelect() {
        // Given
        String simpleQuery = "SELECT * FROM pmw_order_info WHERE product_type = 'residential'";

        // When & Then
        assertThatNoException().isThrownBy(() -> {
            boolean result = sqlQueryValidator.isSimpleSelectQuery(simpleQuery);
            assert result;
        });
    }

    @Test
    void isSimpleSelectQuery_ShouldReturnFalse_WhenQueryContainsUnion() {
        // Given
        String complexQuery = "SELECT * FROM pmw_order_info UNION SELECT * FROM pmw_confirm_info";

        // When & Then
        assertThatNoException().isThrownBy(() -> {
            boolean result = sqlQueryValidator.isSimpleSelectQuery(complexQuery);
            assert !result;
        });
    }
}
