package com.kerryprops.kip.pmw.webservice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.pmw.service.CustomQueryService;
import com.kerryprops.kip.pmw.webservice.resource.CustomQueryRequest;
import com.kerryprops.kip.pmw.webservice.resource.CustomQueryResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for CustomQueryController.
 */
@WebMvcTest(CustomQueryController.class)
class CustomQueryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CustomQueryService customQueryService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void executeCustomQuery_ShouldReturnSuccessResponse_WhenValidQuery() throws Exception {
        // Given
        CustomQueryRequest request = new CustomQueryRequest("SELECT product_type, COUNT(*) FROM pmw_order_info GROUP BY product_type");
        List<String> columns = Arrays.asList("product_type", "count");
        List<List<Object>> rows = Arrays.asList(
            Arrays.asList("residential", 150),
            Arrays.asList("commercial", 75)
        );
        CustomQueryResponse mockResponse = new CustomQueryResponse(columns, rows, 245L, 2);
        
        when(customQueryService.executeCustomQuery(request.getQuery())).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/api/reports/custom-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.columns").isArray())
                .andExpect(jsonPath("$.columns.length()").value(2))
                .andExpect(jsonPath("$.columns[0]").value("product_type"))
                .andExpect(jsonPath("$.columns[1]").value("count"))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.rows.length()").value(2))
                .andExpect(jsonPath("$.rows[0][0]").value("residential"))
                .andExpect(jsonPath("$.rows[0][1]").value(150))
                .andExpect(jsonPath("$.rows[1][0]").value("commercial"))
                .andExpect(jsonPath("$.rows[1][1]").value(75))
                .andExpect(jsonPath("$.execution_time_ms").value(245))
                .andExpect(jsonPath("$.row_count").value(2));
    }

    @Test
    void executeCustomQuery_ShouldReturnBadRequest_WhenValidationFails() throws Exception {
        // Given
        CustomQueryRequest request = new CustomQueryRequest("DELETE FROM pmw_order_info");
        when(customQueryService.executeCustomQuery(request.getQuery()))
            .thenThrow(new IllegalArgumentException("Forbidden keyword detected: DELETE"));

        // When & Then
        mockMvc.perform(post("/api/reports/custom-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void executeCustomQuery_ShouldReturnInternalServerError_WhenServiceThrowsException() throws Exception {
        // Given
        CustomQueryRequest request = new CustomQueryRequest("SELECT * FROM pmw_order_info");
        when(customQueryService.executeCustomQuery(request.getQuery()))
            .thenThrow(new RuntimeException("Database connection error"));

        // When & Then
        mockMvc.perform(post("/api/reports/custom-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void executeCustomQuery_ShouldReturnBadRequest_WhenRequestBodyIsInvalid() throws Exception {
        // Given
        CustomQueryRequest request = new CustomQueryRequest(""); // Empty query

        // When & Then
        mockMvc.perform(post("/api/reports/custom-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void executeCustomQuery_ShouldReturnBadRequest_WhenQueryIsTooLong() throws Exception {
        // Given
        String longQuery = "SELECT * FROM pmw_order_info WHERE " + "a".repeat(10001); // Exceeds 10,000 chars
        CustomQueryRequest request = new CustomQueryRequest(longQuery);

        // When & Then
        mockMvc.perform(post("/api/reports/custom-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void executeCustomQuery_ShouldReturnEmptyResults_WhenNoDataFound() throws Exception {
        // Given
        CustomQueryRequest request = new CustomQueryRequest("SELECT * FROM pmw_order_info WHERE 1=0");
        List<String> columns = Arrays.asList("id", "product_type");
        List<List<Object>> emptyRows = Arrays.asList();
        CustomQueryResponse mockResponse = new CustomQueryResponse(columns, emptyRows, 50L, 0);
        
        when(customQueryService.executeCustomQuery(request.getQuery())).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/api/reports/custom-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.columns").isArray())
                .andExpect(jsonPath("$.columns.length()").value(2))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.rows.length()").value(0))
                .andExpect(jsonPath("$.execution_time_ms").value(50))
                .andExpect(jsonPath("$.row_count").value(0));
    }

    @Test
    void executeCustomQuery_ShouldHaveCorrectEndpointMapping() throws Exception {
        // Given
        CustomQueryRequest request = new CustomQueryRequest("SELECT 1");
        List<String> columns = Arrays.asList("1");
        List<List<Object>> rows = Arrays.asList(Arrays.asList(1));
        CustomQueryResponse mockResponse = new CustomQueryResponse(columns, rows, 10L, 1);
        
        when(customQueryService.executeCustomQuery(anyString())).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(post("/api/reports/custom-query")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }
}
