package com.kerryprops.kip.pmw.webservice;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.pmw.service.ResidenceBillReportService;
import com.kerryprops.kip.pmw.webservice.resource.ResidenceBillReportResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for ResidenceBillReportController.
 */
@WebMvcTest(ResidenceBillReportController.class)
class ResidenceBillReportControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ResidenceBillReportService residenceBillReportService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void getResidenceBillQuarterlyReport_ShouldReturnSuccessResponse_WhenDataExists() throws Exception {
        // Given
        List<ResidenceBillReportResponse> mockData = Arrays.asList(
            new ResidenceBillReportResponse("2024Q1", "residential", "maintenance_fee", 150L, new BigDecimal("45000.00")),
            new ResidenceBillReportResponse("2024Q1", "commercial", "parking_fee", 75L, new BigDecimal("22500.00"))
        );
        when(residenceBillReportService.generateResidenceBillQuarterlyReport()).thenReturn(mockData);

        // When & Then
        mockMvc.perform(get("/api/reports/residence-bills")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].quarter").value("2024Q1"))
                .andExpect(jsonPath("$[0].product_type").value("residential"))
                .andExpect(jsonPath("$[0].category").value("maintenance_fee"))
                .andExpect(jsonPath("$[0].count").value(150))
                .andExpect(jsonPath("$[0].sum_amount").value(45000.00))
                .andExpect(jsonPath("$[1].quarter").value("2024Q1"))
                .andExpect(jsonPath("$[1].product_type").value("commercial"))
                .andExpect(jsonPath("$[1].category").value("parking_fee"))
                .andExpect(jsonPath("$[1].count").value(75))
                .andExpect(jsonPath("$[1].sum_amount").value(22500.00));
    }

    @Test
    void getResidenceBillQuarterlyReport_ShouldReturnEmptyData_WhenNoDataExists() throws Exception {
        // Given
        when(residenceBillReportService.generateResidenceBillQuarterlyReport()).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/reports/residence-bills")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }

    @Test
    void getResidenceBillQuarterlyReport_ShouldReturnErrorResponse_WhenServiceThrowsException() throws Exception {
        // Given
        when(residenceBillReportService.generateResidenceBillQuarterlyReport())
            .thenThrow(new RuntimeException("Database connection error"));

        // When & Then
        mockMvc.perform(get("/api/reports/residence-bills")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }

    @Test
    void getResidenceBillQuarterlyReport_ShouldHaveCorrectEndpointMapping() throws Exception {
        // Given
        when(residenceBillReportService.generateResidenceBillQuarterlyReport()).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/reports/residence-bills"))
                .andExpect(status().isOk());
    }

    @Test
    void getResidenceBillQuarterlyReport_ShouldProduceJsonContentType() throws Exception {
        // Given
        when(residenceBillReportService.generateResidenceBillQuarterlyReport()).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/api/reports/residence-bills"))
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }
}
